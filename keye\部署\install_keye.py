#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye-VL 模型部署脚本
作者: Python猫娘 🐾
功能: 一键安装和部署Keye-VL多模态大语言模型
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description=""):
    """执行命令并处理错误"""
    print(f"🚀 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description} 成功完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Keye-VL需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本检查通过")
    return True

def install_keye_vl():
    """安装Keye-VL"""
    print("=" * 60)
    print("🎯 开始安装Keye-VL多模态大语言模型")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 升级pip
    if not run_command("python -m pip install --upgrade pip", "升级pip"):
        print("⚠️ pip升级失败，继续安装...")
    
    # 安装Keye-VL (高性能版本，包含vLLM)
    install_cmd = 'pip install keye-vl-utils "vllm>=0.9.2"'
    if not run_command(install_cmd, "安装Keye-VL高性能版本"):
        print("❌ 高性能版本安装失败，尝试基础版本...")
        # 如果高性能版本失败，尝试基础版本
        basic_cmd = "pip install keye-vl-utils"
        if not run_command(basic_cmd, "安装Keye-VL基础版本"):
            print("❌ 安装失败，请检查网络连接和Python环境")
            return False
        else:
            print("✅ 基础版本安装成功")
    else:
        print("✅ 高性能版本安装成功")
    
    # 安装其他可能需要的依赖
    additional_deps = [
        "torch",
        "transformers",
        "pillow",
        "requests"
    ]
    
    for dep in additional_deps:
        run_command(f"pip install {dep}", f"安装{dep}")
    
    print("=" * 60)
    print("🎉 Keye-VL安装完成！")
    print("=" * 60)
    
    return True

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye-VL 测试脚本
"""

def test_keye_import():
    """测试Keye-VL导入"""
    try:
        from transformers import AutoModel, AutoTokenizer, AutoProcessor
        from keye_vl_utils import process_vision_info
        print("✅ Keye-VL导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试Keye-VL基本功能...")
    
    if not test_keye_import():
        return False
    
    try:
        # 这里可以添加更多测试
        print("✅ 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Keye-VL 功能测试")
    print("=" * 40)
    
    if test_basic_functionality():
        print("🎉 所有测试通过！Keye-VL已准备就绪")
    else:
        print("❌ 测试失败，请检查安装")
'''
    
    test_file = Path("E:/python/keye/部署/test_keye.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ 测试脚本已创建: {test_file}")

def main():
    """主函数"""
    print("🐾 欢迎使用Keye-VL部署脚本")
    print("由Python猫娘制作 ✨")
    print()
    
    # 安装Keye-VL
    if install_keye_vl():
        # 创建测试脚本
        create_test_script()
        
        print()
        print("📋 后续步骤:")
        print("1. 运行测试: python E:/python/keye/部署/test_keye.py")
        print("2. 查看使用示例: E:/python/keye/部署/usage_examples.py")
        print("3. 开始使用Keye-VL进行多模态AI推理！")
        
    else:
        print("❌ 安装失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()
