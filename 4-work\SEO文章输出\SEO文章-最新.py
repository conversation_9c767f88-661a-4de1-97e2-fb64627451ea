import http.client
import json
import random
import requests
import time
from datetime import datetime
import pandas as pd
import re
import markdown
import os
from bs4 import BeautifulSoup, Tag, NavigableString
from premailer import Premailer
import subprocess
import sys
import hashlib
import tempfile
import base64
import time
import shutil
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException


def calculate_file_md5(filepath):
    """
    计算文件的MD5值。
    Args:
        filepath (str): 文件路径。
    Returns:
        str: 文件的MD5值，如果文件不存在或读取失败，返回None。
    """
    if not os.path.exists(filepath):
        print(f"错误：计算MD5时文件不存在: {filepath}")
        return None
    try:
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            # 分块读取文件以处理大文件
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"错误：计算文件MD5时出错 {filepath}: {e}")
        return None


def extract_doctype_to_head(html_string):
    """
    从HTML字符串中提取从<!DOCTYPE html>到</head>的内容。

    Args:
        html_string (str): 原始的HTML字符串。

    Returns:
        str: 提取的包含<!DOCTYPE html>到</head>的内容, 如果没有找到匹配的内容，返回None。
    """
    pattern = r"(<!DOCTYPE html.*?>.*?</head>)"
    match = re.search(pattern, html_string, re.DOTALL | re.IGNORECASE)  # 使用re.DOTALL匹配换行符，re.IGNORECASE忽略大小写

    if match:
        return match.group(1)
    else:
        return None


def extract_meta_description(html_string):
    """提取 meta description 内容."""
    match = re.search(r'<meta\s+name=["\']description["\']\s+content=["\'](.*?)["\']', html_string, re.IGNORECASE)
    return match.group(1) if match else None


def extract_meta_keywords(html_string):
    """提取 meta keywords 内容."""
    match = re.search(r'<meta\s+name=["\']keywords["\']\s+content=["\'](.*?)["\']', html_string, re.IGNORECASE)
    return match.group(1) if match else None


def extract_h1_content(html_string):
    """提取 <h1> 标签的内容."""
    match = re.search(r'<h1>(.*?)</h1>', html_string, re.IGNORECASE)
    return match.group(1) if match else None


def extract_body_content(html_string):
    """
    从HTML字符串中提取<body> 到</body>之间的全部内容，保留<body>标签，移除<h1>标签及其内容，
    并去除结尾的</body>或者</article>标签。

    Args:
        html_string (str): 原始的HTML字符串.

    Returns:
        str: 提取的<body>和</body>之间的内容，保留<body>标签，移除<h1>标签及其内容,
                 同时还去除了末尾可能存在的</li>和</article>,如果 <body></html>标签不能被找到返回 None.
    """
    body_pattern = r"(<body[^>]*>)(.*?)(</body>)"  # 定义 body 标签的完整pattern.

    body_match = re.search(body_pattern, html_string, re.DOTALL | re.IGNORECASE)  # 搜索<body> 和 </html>
    if not body_match:
        return None

    opening_tag = body_match.group(1)  # <body> 标签  (body属性保留)
    body_content = body_match.group(2)  # <body> 和标签之间的内容, 不包括body标签

    # 首先从   和 标签之间的字符串删除 <h1> 标签：
    body_content_without_h1 = re.sub(r"<h1>.*?</h1>", "", body_content, flags=re.DOTALL | re.IGNORECASE)

    # 清理可能出现的重复标签
    cleaned_content = re.sub(r"(</body>|</article>|</html>)$", "", body_content_without_h1, flags=re.IGNORECASE)
    
    # 移除可能存在的重复的article标签
    cleaned_content = re.sub(r"<article>.*?</article>", "", cleaned_content, flags=re.DOTALL | re.IGNORECASE)

    return opening_tag + cleaned_content.strip()  # 加上opening_tag部分，body的元素不能丢掉


def preprocess_markdown(markdown_content):
    """预处理Markdown，修复特定格式问题，移除TOC，并将非标准标点替换为标准英文标点"""

    # **** 新增：替换非标准标点 ****
    replacements = {
        ''': "'",  # 右单引号 -> 标准撇号
        ''': "'",  # 左单引号 -> 标准撇号
        '"': '"',  # 右双引号 -> 标准双引号
        '"': '"',  # 左双引号 -> 标准双引号
        '–': '-',  # En dash -> 标准连字号
        '—': '-',  # Em dash -> 标准连字号 (或用 '--' 替代)
        '…': '...', # 省略号 -> 三个点
        # 可以根据需要添加更多替换规则
    }
    for old, new in replacements.items():
        markdown_content = markdown_content.replace(old, new)
    # **** 替换结束 ****

    lines = markdown_content.split('\n')
    processed_lines = []
    in_special_table = False
    header_found = False
    separator_found = False
    in_toc_section = False # 新增：跟踪是否在TOC区域

    for i, line in enumerate(lines): # 使用 enumerate 获取索引 i
        original_line = line # 保留原始行用于检查
        stripped = line.strip()
        
        # 检查是否是TOC标题
        if re.match(r'^#+\s*Table\s+of\s+Contents', stripped, re.IGNORECASE):
            in_toc_section = True
            continue # 跳过TOC标题行

        # 如果在TOC区域
        if in_toc_section:
            # 如果是空行或者看起来像列表项（可能带缩进），则跳过
            if not stripped or re.match(r'^\s*[-\*+]\s+\[.*\]\(#.*\)$', stripped) or re.match(r'^\s*[-\*+]\s+.*', stripped):
                 continue # 跳过TOC列表项或空行
            else:
                 # 如果遇到非TOC内容（如下一个标题或普通段落），则结束TOC移除
                 in_toc_section = False
                 # 这一行是TOC之后的内容，需要加入处理
                 pass # 继续后续处理逻辑
        
        # 修正常见的 AI 输出问题，例如标题前的```markdown
        if stripped == '```markdown' or stripped == '```':
            continue
        
        # 新增：处理标题中的##和###符号
        # 标准Markdown标题格式是 '# 标题'，'## 标题'，'### 标题' 等（井号后有空格）
        # 但有时候标题会错误地包含 '## ## 标题' 或 '### ### 标题' 这样的格式
        if stripped.startswith('#'):
            # 检查并修复标题格式
            heading_match = re.match(r'^(#+)\s+(#+\s+.*)', stripped)
            if heading_match:
                # 如果发现类似 '## ## 标题' 的模式，只保留前面的#号和内容
                heading_level = heading_match.group(1)
                content = heading_match.group(2).strip()
                stripped = f"{heading_level} {content.lstrip('#').lstrip()}"
                line = stripped  # 更新行内容
            
            # 清理标题内容中可能出现的额外#号（非开头的）
            if ' ## ' in stripped or ' ### ' in stripped:
                heading_parts = re.match(r'^(#+)\s+(.*)', stripped)
                if heading_parts:
                    heading_level = heading_parts.group(1)
                    content = heading_parts.group(2)
                    # 清理内容中的额外#号
                    content = re.sub(r'\s+#{2,}\s+', ' ', content)
                    stripped = f"{heading_level} {content}"
                    line = stripped  # 更新行内容
        
        # ---- 不再直接删除行首的 '*' 或 '-' ----
        # line = re.sub(r'^\s*[\*\-]\s+', '', line) # <--- 移除这行错误的代码
        # stripped = line.strip() # <--- 移除这行更新

        # **** 确保列表前有空行 ****
        # 检查 *原始行* 是否以 '*' 或 '-' 开头，并且前面有内容但不是空行
        is_potential_list_item = re.match(r'^\s*[\*\-]\s+', original_line.strip())
        if is_potential_list_item and processed_lines and processed_lines[-1].strip() != "":
            # 如果是潜在列表项，且处理过的行列表不为空，且最后一行处理过的不是空行
            last_processed_stripped = processed_lines[-1].strip()
            # 额外检查：确保上一行不是表格分隔符或表格行，也不是另一个列表项
            is_last_line_table_sep = re.match(r'\|\s*:?-+.*:?-+\s*\|', last_processed_stripped)
            is_last_line_table_row = last_processed_stripped.startswith('|')
            is_last_line_list_item = re.match(r'^\s*[\*\-]\s+', last_processed_stripped)

            if not is_last_line_table_sep and not is_last_line_table_row and not is_last_line_list_item:
                 # 如果上一行不是空行、表格相关行或列表项，则在此列表项前插入空行
                 processed_lines.append("") # 在添加当前行之前插入空行

        # ---- 处理特殊表格 ----
        # (这里的逻辑保持不变，但注意它现在操作的是未经 '*' 删除的 line 和 stripped)
        if stripped.startswith('### Goodreads Giveaways vs. Broader Contest Platforms'):
            processed_lines.append(stripped) # Use stripped here as it represents the content line
            in_special_table = True
            header_found = False
            separator_found = False
            continue
        elif stripped.startswith('| Feature |') and 'Goodreads Giveaways' in stripped:
            processed_lines.append(line) # Append original line for table structure
            in_special_table = True
            header_found = True
            separator_found = False
            continue
        elif in_special_table and re.match(r'\|\s*:?-+.*:?-+\s*\|', stripped):
            # 确保分隔符行符合 GFM 要求 (至少三个破折号)
            parts = stripped.split('|')
            corrected_parts = []
            needs_correction = False
            for part in parts:
                if part.strip() and ':' not in part and '-' not in part:
                    continue # 跳过空部分
                if part.strip() and '-' in part and len(re.sub(r'[:\s]', '', part)) < 3:
                     corrected_parts.append(' :---: ') # 修正为至少三个破折号
                     needs_correction = True
                else:
                    corrected_parts.append(part)
            if needs_correction:
                 processed_lines.append('|' + '|'.join(corrected_parts) + '|')
            else:
                 processed_lines.append(line) # Use original line
            separator_found = True
            continue
        elif in_special_table and stripped.startswith('|') and header_found and separator_found:
             parts = line.split('|') # Use original line for splitting
             if len(parts) >= 4:
                 formatted_line = f"| {parts[1].strip()} | {parts[2].strip()} | {parts[3].strip()} |"
                 processed_lines.append(formatted_line)
             else:
                 processed_lines.append(line) # Append original line if format is unexpected
             continue
        elif in_special_table and (not stripped or not stripped.startswith('|')):
             in_special_table = False
             if stripped:
                  cleaned_line = stripped.replace('```', '') # Clean potential ```
                  processed_lines.append(cleaned_line)
        else:
             # 添加非表格区域的行 (确保不在TOC区域)
             if not in_toc_section:
                  cleaned_line = line.replace('```', '') # Clean potential ```
                  processed_lines.append(cleaned_line) # Append the (potentially TOC-skipped) line


    return '\n'.join(processed_lines)


def postprocess_html(html_content):
    """后处理HTML，添加特定的CSS类和高亮 (扩展高亮范围)"""
    
    # 新增：清理H2和H3标题中可能残留的##和###符号
    html_content = re.sub(r'<h2>\s*#{1,2}\s*', '<h2>', html_content)
    html_content = re.sub(r'<h3>\s*#{1,3}\s*', '<h3>', html_content)
    
    # 0. 处理h2和h3标题，为其添加content类的span元素，以适配橙心主题CSS
    h2_pattern = re.compile(r'<h2>(.*?)</h2>', re.DOTALL)
    def h2_replace_func(match):
        h2_content = match.group(1)
        # 额外清理内容中可能的##符号
        h2_content = re.sub(r'\s*#{1,2}\s*', ' ', h2_content).strip()
        return f'<h2><span class="content">{h2_content}</span></h2>'
    html_content = h2_pattern.sub(h2_replace_func, html_content)
    
    h3_pattern = re.compile(r'<h3>(.*?)</h3>', re.DOTALL)
    def h3_replace_func(match):
        h3_content = match.group(1)
        # 额外清理内容中可能的###符号
        h3_content = re.sub(r'\s*#{1,3}\s*', ' ', h3_content).strip()
        return f'<h3><span class="content">{h3_content}</span></h3>'
    html_content = h3_pattern.sub(h3_replace_func, html_content)
    
    # 1. 包装并添加表格类
    table_pattern = re.compile(r'<table>(.*?)</table>', re.DOTALL)
    wrapped_html_content = html_content # 操作副本
    matches = list(table_pattern.finditer(html_content))
    if matches:
        # 只包装第一个找到的表格
        match = matches[0]
        table_inner_html = match.group(1)
        wrapped_table = f'<div class="table-wrapper"><table class="comparison-table">{table_inner_html}</table></div>'
        wrapped_html_content = html_content[:match.start()] + wrapped_table + html_content[match.end():]
        
    html_content = wrapped_html_content

    # 2. 添加表头类 
    html_content = html_content.replace('<th>Feature</th>', '<th class="feature-column">Feature</th>', 1)
    html_content = html_content.replace('<th>Goodreads Giveaways</th>', '<th class="goodreads-column">Goodreads Giveaways</th>', 1)
    html_content = re.sub(r'(<th class="goodreads-column">.*?</th>\s*<th>)(.*?)(</th>)', r'\1<th class="platforms-column">\2\3', html_content, 1)

    # 3. 为特征列单元格添加类 (先添加类)
    lines = html_content.split('\n')
    processed_lines = []
    for line in lines:
        if line.strip().startswith('<tr>'):
            line = re.sub(r'(<tr>\s*)<td>', r'\1<td class="feature-cell">', line, 1)
        processed_lines.append(line)
    html_content = '\n'.join(processed_lines)
    
    # 4. 全局关键字高亮 (处理多种标签)
    all_keywords = [
        "Target Audience", "Primary Goal", "Customization", "Entry Mechanics", 
        "Analytics", "Platform", "Cost", "Goodreads Giveaways", "context and intent"
    ]
    tags_to_highlight_in = ['p', 'li', 'td', 'th', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
    tag_pattern = '|'.join(tags_to_highlight_in)
    
    for keyword in all_keywords:
        escaped_keyword = re.escape(keyword)
        pattern = re.compile(r"\b(" + escaped_keyword + r")\b(?![^<]*>)", re.IGNORECASE)
        
        def replace_func(match): 
            return f'<span class="highlight-word">{match.group(1)}</span>'
        
        html_content = pattern.sub(replace_func, html_content)

    return html_content


def convert_markdown_with_lib(markdown_text):
    """使用 markdown 库转换 Markdown 到 HTML"""
    processed_md = preprocess_markdown(markdown_text)
    try:
        html = markdown.markdown(processed_md, extensions=['tables', 'fenced_code', 'extra'])
    except Exception as e:
        print(f"Markdown库转换出错: {e}")
        print("--- 预处理后的Markdown内容 ---")
        print(processed_md)
        print("-------------------------------")
        return f"<p>Error during Markdown conversion. Preprocessed content:</p><pre>{processed_md}</pre>"
        
    final_html = postprocess_html(html)
    return final_html


def load_css_from_file(css_filepath):
    """从文件加载CSS内容"""
    script_dir = os.path.dirname(os.path.abspath(__file__)) # 获取脚本绝对路径
    absolute_css_path = os.path.join(script_dir, css_filepath)
    try:
        with open(absolute_css_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误：未找到CSS文件 {absolute_css_path}")
        return "/* CSS file not found */ body { font-family: sans-serif; }"
    except Exception as e:
        print(f"读取CSS文件 {absolute_css_path} 时出错: {e}")
        return "/* Error reading CSS file */"


def to_html(markdown_content, keyword):
    """使用 markdown 库将文章内容转换为faisco主题样式的完整HTML页面，并返回HTML和标题"""
    print("使用 markdown 库和faisco主题转换 HTML，优化Faisco博客平台显示...")
    
    # 1. 从 Markdown 内容提取标题 (优先 H1)
    title = "Generated Article" # 默认标题
    lines = markdown_content.strip().split('\n')
    first_h1_match = re.search(r'^#\s+(.+)', markdown_content, re.MULTILINE)
    if first_h1_match:
        original_title = first_h1_match.group(1).strip()
        print(f"原始标题: {original_title}")
        print(f"标题长度: {len(original_title)} 字符")

        # 如果标题太长，自动提示但不要求修改
        if len(original_title) > 80:
            print("警告：标题过长，超过80字符。建议控制在60-80字符以内以获得更好的SEO效果")
            # 提取关键词生成更简洁的标题建议
            keywords = re.findall(r'\b\w{4,}\b', original_title.lower())
            important_keywords = [k for k in keywords if k not in ['with', 'and', 'the', 'that', 'this', 'for', 'from', 'your', 'how', 'what', 'when', 'where', 'why']]
            suggested_title = ' '.join(important_keywords[:4]).title()
            print(f"建议的简短标题: {suggested_title}")
            # 可以在这里考虑是否直接使用 suggested_title，或者让用户决定
            # title = suggested_title # 如果想自动替换长标题
        title = original_title # 仍然使用原始H1标题，即使过长（之前的逻辑）
    else:
        print("警告：未找到H1标题，将尝试使用第一个有效行作为标题。") # 添加提示
        for line in lines:
            stripped_line = line.strip()
            if stripped_line and not stripped_line.startswith('#'): # 找第一个非标题行
                 # 进一步清理可能的残留标记
                 fallback_title = re.sub(r'[`*>\[\]!\-|#]+', '', stripped_line).strip() # 清理井号等
                 if fallback_title:
                     # **** 新增：对回退标题进行截断 ****
                     max_fallback_length = 100 # 设置一个最大长度，例如100字符
                     if len(fallback_title) > max_fallback_length:
                         print(f"警告：回退标题过长({len(fallback_title)}字符)，将截断为 {max_fallback_length} 字符。")
                         title = fallback_title[:max_fallback_length] + "..."
                     else:
                         title = fallback_title
                     # **** 截断结束 ****
                     break # 找到第一个有效行就停止
            # 注意：移除了之前查找其他级别标题的逻辑，优先使用第一个有效内容行截断后的结果
            # elif stripped_line.startswith('#'): ... (移除这部分逻辑)

    if not title or title == "Generated Article": # 如果上面的逻辑没找到合适的标题
        title = "Default Article Title" # 最后的默认值

    print(f"最终提取/生成的标题: {title}") # 修改打印信息，显示最终使用的标题

    # 2. 生成描述 (取处理后文本的前160字符)
    text_for_desc = re.sub(r'[#*`>\[\]!\-|]+', '', markdown_content)
    text_for_desc = re.sub(r'\n\s*\n', ' \n', text_for_desc) 
    description = ' '.join(text_for_desc.split()).strip()[:160]
    if len(description) == 160:
        description += '...'
    print(f"生成描述: {description[:50]}...")

    # 3. 生成关键词 (仅使用传入的keyword，不添加其他关键词)
    keywords_str = keyword.lower()
    print(f"使用关键词: {keywords_str}")

    # 4. 使用 markdown 库转换 Markdown 为 HTML body
    html_body_content = convert_markdown_with_lib(markdown_content)

    # 5. 移除H1标题前的内容
    soup = BeautifulSoup(html_body_content, 'html.parser')
    h1 = soup.find('h1')
    if h1:
        # 移除h1之前的所有元素
        for elem in list(h1.previous_siblings):
            elem.decompose()
        # 更新html_body_content
        html_body_content = str(soup)

    # 6. 添加Faisco博客平台特定的类和样式
    # 为H1, H2, H3添加特定类
    html_body_content = re.sub(r'<h1>(.*?)</h1>', r'<h1 class="faisco-title">\1</h1>', html_body_content)
    html_body_content = re.sub(r'<h2><span class="content">(.*?)</span></h2>', 
                             r'<h2 class="faisco-section"><span class="content">\1</span></h2>', 
                             html_body_content)
    html_body_content = re.sub(r'<h3><span class="content">(.*?)</span></h3>', 
                             r'<h3 class="faisco-subsection"><span class="content">\1</span></h3>', 
                             html_body_content)
    
    # 为列表添加Faisco特定类
    html_body_content = re.sub(r'<ul>', r'<ul class="faisco-list">', html_body_content)
    html_body_content = re.sub(r'<ol>', r'<ol class="faisco-ordered-list">', html_body_content)
    
    # 为段落和链接添加类
    html_body_content = re.sub(r'<p>', r'<p class="faisco-paragraph">', html_body_content)
    html_body_content = re.sub(r'<a\s+href="([^"]+)"', r'<a class="faisco-link" href="\1"', html_body_content)

    # --- 重新加入固定的底部内容 --- 
    bottom_content = '''
        <h2 class="faisco-section" style="color:#0056b3;font-size:2rem;margin-bottom:15px;">
        Transform Your Marketing with Faisco: Gamify, Go Viral, Grow Faster
        </h2>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
        Tired of seeing great marketing ideas stuck in development limbo? Want to launch interactive campaigns that not only engage but explode organically, driving predictable growth? Meet Faisco, your all-in-one SaaS platform for <strong>gamified marketing and lightning-fast viral growth</strong>. Design and deploy high-converting contests, engaging quizzes, viral giveaways, and interactive lead-capture forms in minutes – absolutely no coding needed. Faisco provides an unfair advantage for achieving measurable, engagement-driven marketing success.
        </p>
        <h3 class="faisco-subsection" style="color:#0056b3;font-size:1.5rem;margin-bottom:15px;">
        Launch Instantly with 100+ Proven &amp; Customizable Campaign Templates
        </h3>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
        Stop starting from scratch. Jumpstart your user acquisition and build lasting customer engagement with our arsenal of over 100 professionally designed, <strong>battle-tested gamified templates</strong>. Effortlessly launch captivating spin-to-wins, viral giveaways, competitions, leaderboards, and engaging games in mere minutes. Each template is engineered for maximum participation, shares, and high-quality conversion rates, ensuring your campaigns hit the ground running. No technical skills required - just your creativity.
        </p>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
            <a _i="AGQIcBIA" _n="Template" _t="100" astyle_h="1" class="faisco-link" href="/en/template.html" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" _srchref="template.html" data_ue_src="/en/template.html">Click to see more exquisite campaign templates.</a>
        </p>
        <p>
            <a class="switchJump" href="https://faisco.com/" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" data_ue_src="https://faisco.com/"><img alt="Built-in viral marketing tools and social sharing features visualization" src="//********.s21i.faiusr.com/4/ABUIABAEGAAgmbWWtgYo8K71yQEwvAU4pAM.png" style="max-width:100%;height:auto;border-radius:5px;display:block;margin:20px auto;" width="1000" /></a>
        </p>
        <h3 class="faisco-subsection" style="color:#0056b3;font-size:1.5rem;margin-bottom:15px;">
        Unlock Explosive Growth with Our Built-In Viral Marketing Engine
        </h3>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
        Go beyond basic sharing and truly ignite word-of-mouth. Faisco's integrated viral marketing toolkit is designed to supercharge your organic reach and turn your audience into your most effective advocates:
        <ul class="faisco-list" style="list-style-type:disc;padding-left:1.5em;margin-left:20px;margin-bottom:15px;">
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Smart Social Sharing: </strong>Seamless one-click sharing optimized for today's top social platforms (X/Twitter, Facebook, Instagram, LinkedIn &amp; more).</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Incentivized Referrals &amp; Viral Loops: </strong>Motivate users to spread the word with customizable rewards and Points Systems, dramatically boosting your campaign's K-factor (viral coefficient).</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Automated Network Effects: </strong>Watch your participant numbers and brand mentions multiply as Faisco's system encourages natural, exponential amplification of your campaign message.</li></ul>
        </p>
        <p>
            <a class="switchJump" href="https://faisco.com/" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" data_ue_src="https://faisco.com/"><img alt="Brand integration ecosystem and multi-channel campaign management interface" src="//********.s21i.faiusr.com/4/ABUIABAEGAAggPjotwYowJrBogUw_Ao4_AU.png" style="max-width:100%;height:auto;border-radius:5px;display:block;margin:20px auto;" width="1000" /></a>
        </p>
        <h3 class="faisco-subsection" style="color:#0056b3;font-size:1.5rem;margin-bottom:15px;">
        Amplify Your Reach with Seamless Multi-Channel Distribution
        </h3>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
        Don't limit your campaign's potential. Faisco empowers you to:
        <ul class="faisco-list" style="list-style-type:disc;padding-left:1.5em;margin-left:20px;margin-bottom:15px;">
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Effortlessly Distribute:</strong> Push your interactive campaigns across all major touchpoints – embed directly onto your website or landing pages, share via unique links on social media, include in email newsletters, and more.</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Maximize Social Proof &amp; Brand Consistency:</strong> Our optimized sharing framework ensures your campaign looks professional and functions flawlessly everywhere, strengthening vital social proof and maximizing word-of-mouth potential where your customers live online.</li></ul>
        </p>
        <p>
            <a class="switchJump" href="https://faisco.com/" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" data_ue_src="https://faisco.com/"><img alt="Go Viral With Your Brand" src="//********.s21i.faiusr.com/4/ABUIABAEGAAgtNSFtgYora7k-gUwsAk4xAQ.png" style="max-width:100%;height:auto;border-radius:5px;display:block;margin:20px auto;" width="1000" /></a>
        </p>
        <h3 class="faisco-subsection" style="color:#0056b3;font-size:1.5rem;margin-bottom:15px;">
        Optimize &amp; Scale with Actionable Data-Driven Insights
        </h3>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:1.8;max-width:100%;margin-bottom:15px;">
        Stop guessing, start growing strategically. Faisco's comprehensive analytics dashboard translates raw data into your actionable growth plan:
        <ul class="faisco-list" style="list-style-type:disc;padding-left:1.5em;margin-left:20px;margin-bottom:15px;">
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Real-Time Performance Tracking: </strong>Monitor crucial KPIs live, including participant engagement rates, detailed conversion funnels, viral lift (amplification rate), sources of traffic, and campaign ROI.</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Gamification Effectiveness Analysis: </strong>Understand which interactive elements and game mechanics truly captivate your audience and drive desired actions.</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        <strong>Optimize for Virality &amp; ROI: </strong>Identify your most influential advocates and most effective sharing channels to continuously refine your approach, ensuring every campaign dollar works harder.</li></ul>
        </p>
        <p>
            <a class="switchJump" href="https://faisco.com/" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" data_ue_src="https://faisco.com/"><img alt="Real-Time Analytics &amp; Actionable Insights" src="//********.s21i.faiusr.com/4/ABUIABAEGAAggPjotwYo_Zu89AYw_Ao4_AU.png" style="max-width:100%;height:auto;border-radius:5px;display:block;margin:20px auto;" width="1000" /></a>
        </p>
        <h3 class="faisco-subsection" style="color:#0056b3;font-size:1.5rem;margin-bottom:15px;">
        Ready to Experience the Faisco Effect? Launch Your First Viral Campaign in Under 3 Minutes
        </h3>
        <p class="faisco-paragraph" style="color:rgb(0, 0, 0); font-size:18px; line-height:2.1em; max-width:100%; margin-bottom:15px;">
        Seeing is believing. Turn marketing theory into tangible results and witness the power of easy, gamified, viral marketing firsthand.
        Try Faisco Absolutely Free: <a href="https://i.faisco.com" astyle_h="1" data_ue_src="https://i.faisco.com" lsc="false" _t="103" _i="AGcI/////w8SFGh0dHBzOi8vaS5mYWlzY28uY29t" _n="https://i.faisco.com" target="_blank" textvalue="Click Here to Start Your Free Trial" style="color:rgb(255, 0, 0); text-decoration:underline;">Click Here to Start Your Free Trial</a>
        <ul class="faisco-list" style="list-style-type:disc;padding-left:1.5em;margin-left:20px;margin-bottom:15px;">
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        No credit card required to start.</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        Experience just how simple it is to build and launch an engaging, professional-grade campaign in minutes.</li>
        <li style="color:#333;font-size:16px;line-height:1.7;margin-bottom:10px;">
        It's the perfect, no-risk way to explore our powerful template library and viral marketing tools. Say goodbye to weeks of custom development and hello to instant engagement!</li></ul>
        </p>
        <p class="faisco-paragraph" style="color:#000000;font-size:18px;line-height:2.1em;max-width:100%;margin-bottom:15px;">
        Ready to consistently exceed your marketing goals? <a href="/en/price.html" astyle_h="1" data_ue_src="/en/price.html" lsc="false" _t="100" _i="AGQIbxIA" _n="Pricing" target="_blank" textvalue="Explore our Transparent Pricing Plans and Choose Your Growth Path" style="color:rgb(255, 0, 0); text-decoration:underline;">Explore our Transparent Pricing Plans and Choose Your Growth Path</a>
        </p>
        <p>
        <a class="switchJump" href="https://faisco.com/" rel="noopener noreferrer" style="color:#ff5722;text-decoration:underline;transition:color 0.3s ease;" target="_blank" data_ue_src="https://faisco.com/"><img alt="FAISCO intuitive campaign creation workflow with drag-and-drop interface" src="//********.s21i.faiusr.com/4/ABUIABAEGAAgz6iWtgYoxsKk3wcw6Ac45gE.png" style="max-width:100%;height:auto;border-radius:5px;display:block;margin:20px auto;" width="1000" /></a>
   '''
    # --- 结束底部内容 ---

    # 合并主体内容和底部内容
    full_body_content = html_body_content + '\n' + bottom_content

    # 7. 构建完整 HTML 页面
    escaped_title = json.dumps(title)[1:-1] 
    escaped_description = json.dumps(description)[1:-1]
    escaped_keywords_str = json.dumps(keywords_str)[1:-1]
    
    full_html = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{escaped_description}">
    <meta name="keywords" content="{escaped_keywords_str}">
    <meta name="author" content="Faisco Marketing Team">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="{escaped_title}">
    <meta property="og:description" content="{escaped_description}">
    <meta property="og:type" content="article">
    <title>{escaped_title}</title>
    <style>
    /* CSS content will be applied as inline styles later */
    </style>
    <script type="application/ld+json">
    {{
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "{escaped_title}",
      "description": "{escaped_description}",
      "author": {{
        "@type": "Organization",
        "name": "Faisco Marketing Team"
      }},
      "publisher": {{
        "@type": "Organization",
        "name": "Faisco",
        "logo": {{
          "@type": "ImageObject",
          "url": "https://faisco.com/logo.png"
        }}
      }},
      "datePublished": "{str(datetime.now().date())}",
      "keywords": "{escaped_keywords_str}"
    }}
    </script>
</head>
<body>
    {full_body_content}
</body>
</html>'''

    # 8. 创建内联样式版本的HTML
    print("使用自定义内联样式转换")
    inline_html = convert_css_to_inline_styles(full_html, "")

    # 9. 最后的 BeautifulSoup 调整
    try:
        soup = BeautifulSoup(inline_html, 'html.parser')
        
        # 确保所有图片都是响应式的
        for img in soup.find_all('img'):
            if 'style' not in img.attrs:
                img['style'] = ''
            if 'max-width: 100%' not in img['style']:
                img['style'] += '; max-width: 100%; height: auto; display: block; margin: 20px auto;'
                
        # 确保所有表格都有正确的样式
        for table in soup.find_all('table'):
            # 包裹表格
            if not table.parent or table.parent.name != 'div' or 'table-wrapper' not in table.parent.get('class', []):
                 wrapper = soup.new_tag('div', attrs={'class': 'table-wrapper'})
                 wrapper['style'] = 'overflow-x: auto; margin: 20px 0;'
                 table.wrap(wrapper)
            if 'style' not in table.attrs:
                table['style'] = ''
            # Remove redundant width setting if wrapper handles it
            # if 'width: 100%' not in table['style']:
            #    table['style'] += '; width: 100%; border-collapse: collapse; margin: 20px 0;'
            table['style'] += '; width: 100%; border-collapse: collapse; margin: 0;' # Set margin to 0 as wrapper handles it

            for th in table.find_all('th'):
                if 'style' not in th.attrs:
                    th['style'] = ''
                # Removed background color override, rely on inline function
                # th['style'] += '; background: rgba(239, 112, 96, 0.1); padding: 10px; text-align: left; border: 1px solid #ddd;'
                th['style'] += '; padding: 10px; text-align: left; border: 1px solid #ddd;'


            for td in table.find_all('td'):
                if 'style' not in td.attrs:
                    td['style'] = ''
                td['style'] += '; padding: 12px; border: 1px solid #ddd;'

        # 更新内联HTML
        inline_html = str(soup)
        print("完成博客平台特定调整")
    except Exception as e:
        print(f"博客平台特定调整失败：{e}")

    # 10. 返回内联HTML 和 提取的标题
    return inline_html, title


def analyze_keyword_emotion(keyword):
    """
    根据关键词分析应该使用的情绪类型
    返回情绪类型和对应的Byron情绪表达特点
    """
    keyword_lower = keyword.lower()

    # 竞争和挑战类关键词 - 激动、挑战性情绪
    competitive_keywords = [
        'vs', 'versus', 'compare', 'comparison', 'alternative', 'competitor',
        'battle', 'fight', 'challenge', 'beat', 'win', 'defeat', 'outperform',
        'better than', 'superior', 'advantage', 'edge', 'dominate'
    ]

    # 成功和成长类关键词 - 热情、自信情绪
    success_keywords = [
        'growth', 'success', 'increase', 'boost', 'improve', 'enhance',
        'maximize', 'optimize', 'scale', 'expand', 'achieve', 'reach',
        'viral', 'explosive', 'breakthrough', 'transform', 'revolution'
    ]

    # 问题和解决方案类关键词 - 关切、解决问题的决心
    problem_keywords = [
        'problem', 'issue', 'challenge', 'struggle', 'difficulty', 'pain',
        'solution', 'fix', 'solve', 'resolve', 'overcome', 'tackle',
        'mistake', 'error', 'fail', 'wrong', 'avoid', 'prevent'
    ]

    # 创新和趋势类关键词 - 兴奋、前瞻性情绪
    innovation_keywords = [
        'new', 'latest', 'trend', 'future', 'innovation', 'cutting-edge',
        'advanced', 'modern', 'next-gen', 'revolutionary', 'breakthrough',
        '2024', '2025', 'emerging', 'disruptive', 'game-changing'
    ]

    # 分析关键词匹配情绪类型
    if any(comp in keyword_lower for comp in competitive_keywords):
        return "competitive", {
            "base_tone": "激动和挑战性",
            "expression_style": "更直接、有力的语言，使用对比和竞争性词汇",
            "personal_touch": "分享竞争经验和胜利案例",
            "energy_level": "高能量，略带攻击性"
        }
    elif any(succ in keyword_lower for succ in success_keywords):
        return "enthusiastic", {
            "base_tone": "热情和自信",
            "expression_style": "积极向上的语言，强调成果和成就",
            "personal_touch": "分享成功故事和兴奋的发现",
            "energy_level": "高度热情，感染力强"
        }
    elif any(prob in keyword_lower for prob in problem_keywords):
        return "concerned_helpful", {
            "base_tone": "关切和解决问题的决心",
            "expression_style": "同理心强的语言，专注于理解和解决",
            "personal_touch": "分享帮助客户解决问题的经历",
            "energy_level": "稳重但坚定，充满责任感"
        }
    elif any(innov in keyword_lower for innov in innovation_keywords):
        return "excited_forward", {
            "base_tone": "兴奋和前瞻性",
            "expression_style": "充满好奇心的语言，强调可能性和机会",
            "personal_touch": "分享对新趋势的观察和预测",
            "energy_level": "充满好奇心和期待"
        }
    else:
        # 默认情绪 - 专业但友好
        return "professional_friendly", {
            "base_tone": "专业但友好",
            "expression_style": "平衡的语言，既专业又易于理解",
            "personal_touch": "分享相关的实际经验",
            "energy_level": "稳定、可信赖"
        }


def generate_anti_ai_detection_prompt(emotion_type, keyword):
    """
    生成反AI检测的提示词指导
    基于最新的反AI检测技术（2024年）
    """
    base_anti_detection = f"""
**CRITICAL: Anti-AI Detection Instructions for [{keyword}]**

You MUST write like a real human expert, not an AI. Here's how to make your writing undetectable:

**Perplexity & Burstiness (2024 technique):**
- Mix short punchy sentences with longer, complex ones
- Use unexpected word choices and varied sentence structures
- Include some run-on sentences and fragments naturally
- Vary paragraph lengths dramatically (some 1 line, others 6+ lines)

**Human Imperfections (Essential):**
- Include 1-2 minor grammatical quirks or informal constructions
- Use contractions inconsistently (sometimes "do not", sometimes "don't")
- Add occasional filler words: "you know", "I mean", "honestly"
- Include slight repetition or circular thinking patterns
- Use incomplete thoughts followed by clarification

**Byron's Personal Language Habits:**
- Start some sentences with "Look," or "Here's the thing,"
- Use "honestly" and "frankly" when expressing strong opinions
- Occasionally interrupt yourself with parenthetical thoughts
- Include mild skepticism about trendy marketing terms
- Reference specific years/timeframes from your experience

**Emotional Authenticity:**
- Show genuine frustration with bad marketing practices
- Express real excitement about solutions that work
- Include moments of uncertainty or self-correction
- Add personal anecdotes that feel spontaneous, not scripted
- Let your personality show through word choice and tone

**Technical Randomness:**
- Vary your expertise level explanations (sometimes detailed, sometimes casual)
- Include tangential thoughts that connect back to the main point
- Use industry jargon inconsistently (explain some terms, assume others)
- Reference competitors or tools without always explaining them fully
"""

    # 根据情绪类型添加特定的反检测策略
    emotion_specific = {
        "competitive": """
**For this competitive topic:**
- Let your competitive edge show through slightly aggressive language
- Include personal wins/losses in similar situations
- Use more direct, challenging statements
- Show impatience with inferior solutions
""",
        "enthusiastic": """
**For this exciting topic:**
- Let enthusiasm create some run-on sentences
- Include exclamation points sparingly but naturally
- Show genuine surprise at good results
- Use more energetic, varied vocabulary
""",
        "concerned_helpful": """
**For this problem-solving topic:**
- Show empathy through softer, more careful language
- Include hesitation when discussing complex issues
- Use more qualifying statements ("usually", "often", "in my experience")
- Express genuine concern for business owners' struggles
""",
        "excited_forward": """
**For this innovative topic:**
- Show curiosity through questions and speculation
- Include some uncertainty about future predictions
- Use more exploratory language ("I'm seeing", "it seems like")
- Express genuine fascination with new developments
""",
        "professional_friendly": """
**For this balanced topic:**
- Maintain steady, conversational tone
- Include practical concerns and realistic expectations
- Use measured language with occasional personal touches
- Show experience through casual confidence
"""
    }

    return base_anti_detection + emotion_specific.get(emotion_type, emotion_specific["professional_friendly"])


# 已移除 analyze_paragraph_ai_intensity 函数
# 原因：重写功能破坏了Markdown结构，暂时移除


# 已移除 extract_markdown_structure 函数
# 原因：重写功能破坏了Markdown结构，暂时移除


# 已移除 rewrite_paragraph_with_format 函数
# 原因：重写功能破坏了Markdown结构，暂时移除


# 已移除 iterative_content_optimization 函数
# 原因：重写功能破坏了Markdown结构，暂时移除


def generate_dynamic_faisco_cases():
    """
    动态生成Faisco案例，避免固化重复
    """
    import random

    # 城市池（避免重复使用Denver、Toronto、Seattle）
    cities = [
        "Austin", "Portland", "Nashville", "Charlotte", "Phoenix", "San Diego",
        "Boston", "Atlanta", "Miami", "Chicago", "Vancouver", "Montreal",
        "Calgary", "Ottawa", "Minneapolis", "Milwaukee", "Tampa", "Orlando"
    ]

    # 行业池
    businesses = [
        {"type": "coffee shop", "action": "Instagram followers"},
        {"type": "fitness studio", "action": "email addresses"},
        {"type": "bakery", "action": "user-generated posts on TikTok"},
        {"type": "bookstore", "action": "Facebook page likes"},
        {"type": "yoga studio", "action": "newsletter subscribers"},
        {"type": "pet grooming salon", "action": "Google reviews"},
        {"type": "craft brewery", "action": "Instagram story views"},
        {"type": "flower shop", "action": "Pinterest followers"},
        {"type": "barbershop", "action": "local community members"},
        {"type": "art gallery", "action": "event attendees"}
    ]

    # Faisco游戏类型池
    games = [
        "Lucky Spin", "Whac-A-Mole", "Quick Catch", "Scratch Ticket",
        "Burger Stacker", "Crazy Karting", "Star Seeker", "Pet Match & Win",
        "Unlock Lucky Words", "Puzzle Challenge", "Summer Catch", "Happy Hopping"
    ]

    # 生成三个随机案例
    cases = []
    used_cities = []
    used_businesses = []

    for i in range(3):
        # 确保不重复城市和行业
        available_cities = [c for c in cities if c not in used_cities]
        available_businesses = [b for b in businesses if b not in used_businesses]

        city = random.choice(available_cities)
        business = random.choice(available_businesses)
        game = random.choice(games)

        # 生成合理的数据范围
        if "followers" in business["action"] or "likes" in business["action"]:
            number = random.randint(650, 1200)
        elif "email" in business["action"] or "subscribers" in business["action"]:
            number = random.randint(800, 1500)
        elif "posts" in business["action"] or "views" in business["action"]:
            number = random.randint(1800, 3200)
        elif "reviews" in business["action"]:
            number = random.randint(45, 85)
        elif "members" in business["action"] or "attendees" in business["action"]:
            number = random.randint(120, 280)
        else:
            number = random.randint(500, 1000)

        # 时间范围
        timeframe = random.choice(["2 weeks", "10 days", "3 weeks", "12 days", "2.5 weeks"])

        case = f'I\'ve deployed their "{game}" for a {city} {business["type"]} that got them {number} new {business["action"]} in {timeframe}'
        cases.append(case)

        used_cities.append(city)
        used_businesses.append(business)

    return cases


def generate_anti_ai_detection_prompt(emotion_type, keyword):
    """
    生成反AI检测的提示词指导
    基于最新的反AI检测技术（2024年）- 强化版
    """
    base_anti_detection = f"""
**CRITICAL: Anti-AI Detection Instructions for [{keyword}] - MAXIMUM HUMANIZATION**

You MUST write like a real human expert with quirks, NOT an AI. Make this completely undetectable:

**Extreme Perplexity & Burstiness:**
- Mix 3-word fragments with 25+ word run-on sentences randomly
- Use weird word choices that a human might pick (like "honestly" instead of "truly")
- Include incomplete thoughts that trail off... then pick up elsewhere
- Make some paragraphs just one sentence. Others should be massive walls of text.
- Use dashes randomly—like this—for no particular reason

**Human Imperfections (MANDATORY):**
- Include 2-3 minor grammar mistakes or awkward phrasings
- Use contractions super inconsistently (mix "don't" and "do not" randomly)
- Add filler words everywhere: "you know", "I mean", "honestly", "like", "basically"
- Repeat yourself slightly or circle back to points
- Start sentences then change direction mid-thought
- Use "..." frequently for dramatic pauses or uncertainty

**Byron's Specific Quirks (ESSENTIAL):**
- Start random sentences with "Look," "Listen," "Here's the thing," "Honestly,"
- Use "frankly" when you're about to be blunt
- Interrupt yourself with (random parenthetical thoughts that may not be super relevant)
- Show mild irritation with buzzwords: "I hate when people say 'viral' but..."
- Reference oddly specific timeframes: "back in 2018", "around March 2020"
- Use "I've been doing this since..." frequently

**Emotional Chaos (Human-like):**
- Show genuine annoyance at stupid marketing advice
- Get genuinely excited and use multiple exclamation points occasionally!!
- Include moments where you second-guess yourself: "Actually, wait..."
- Add random personal tangents that barely relate to the topic
- Use emotional language inconsistently (sometimes professional, sometimes casual)

**Technical Inconsistency:**
- Explain basic concepts sometimes, assume knowledge other times
- Use industry jargon randomly without explanation
- Reference tools/competitors casually like everyone knows them
- Include half-finished technical thoughts
- Mix very detailed explanations with super casual ones

**Random Human Behaviors:**
- Include typos that got "corrected" but still look weird
- Use numbers inconsistently (sometimes "3" sometimes "three")
- Add random emphasis with CAPS or *asterisks*
- Include rhetorical questions that you answer yourself
- Use colloquialisms and regional expressions
"""

    # 根据情绪类型添加特定的反检测策略
    emotion_specific = {
        "competitive": """
**For this competitive topic:**
- Let your competitive edge show through slightly aggressive language
- Include personal wins/losses in similar situations
- Use more direct, challenging statements
- Show impatience with inferior solutions
""",
        "enthusiastic": """
**For this exciting topic:**
- Let enthusiasm create some run-on sentences
- Include exclamation points sparingly but naturally
- Show genuine surprise at good results
- Use more energetic, varied vocabulary
""",
        "concerned_helpful": """
**For this problem-solving topic:**
- Show empathy through softer, more careful language
- Include hesitation when discussing complex issues
- Use more qualifying statements ("usually", "often", "in my experience")
- Express genuine concern for business owners' struggles
""",
        "excited_forward": """
**For this innovative topic:**
- Show curiosity through questions and speculation
- Include some uncertainty about future predictions
- Use more exploratory language ("I'm seeing", "it seems like")
- Express genuine fascination with new developments
""",
        "professional_friendly": """
**For this balanced topic:**
- Maintain steady, conversational tone
- Include practical concerns and realistic expectations
- Use measured language with occasional personal touches
- Show experience through casual confidence
"""
    }

    return base_anti_detection + emotion_specific.get(emotion_type, emotion_specific["professional_friendly"])


def generate_emotion_prompt_section(emotion_type, emotion_data, keyword):
    """
    根据情绪类型生成对应的提示词部分
    """
    emotion_prompts = {
        "competitive": f"""
**Your Emotional State for this topic:**
You're feeling particularly fired up about [{keyword}] because you've seen too many businesses get burned by making the wrong choices in this area. You've got that slightly competitive edge that comes from 15 years of watching companies succeed or fail based on these decisions. You're not just sharing information - you're sharing battle-tested insights that could make or break someone's marketing efforts.

**How this affects your writing:**
- Use more direct, assertive language ("Here's what actually works..." "Don't fall for the hype about...")
- Share specific examples of competitors or alternatives you've seen fail
- Include phrases like "I've seen this play out dozens of times" or "Every single client who tried X ended up..."
- Show some passion when discussing what really works vs. what's just marketing fluff
- Use competitive language naturally ("outperform", "beat the competition", "get ahead")
""",

        "enthusiastic": f"""
**Your Emotional State for this topic:**
You're genuinely excited about [{keyword}] because you've seen it create real, measurable results for your clients. This is one of those topics that gets you energized because you know it can be a game-changer for small businesses. You've got that enthusiasm that comes from seeing something actually work in the real world.

**How this affects your writing:**
- Use more energetic, positive language ("This is where it gets really interesting..." "I love seeing this in action...")
- Share success stories with genuine excitement ("I still remember when..." "You should have seen the results...")
- Include phrases like "This is exactly why I'm passionate about..." or "It's incredible how..."
- Let your enthusiasm show through specific numbers and achievements
- Use growth-oriented language naturally ("explosive", "breakthrough", "transform")
""",

        "concerned_helpful": f"""
**Your Emotional State for this topic:**
You're feeling that familiar concern that comes from seeing too many small businesses struggle with [{keyword}]. This hits close to home because you've personally helped dozens of business owners who were frustrated, confused, or had been burned by bad advice. You're in full problem-solver mode.

**How this affects your writing:**
- Use more empathetic, understanding language ("I know how frustrating this can be..." "You're not alone in this...")
- Share stories of clients who faced similar challenges
- Include phrases like "I've been there" or "I see this all the time" or "Let me help you avoid..."
- Focus on practical, actionable solutions
- Use supportive language naturally ("overcome", "solve", "get through this")
""",

        "excited_forward": f"""
**Your Emotional State for this topic:**
You're genuinely excited about [{keyword}] because you can see where the industry is heading, and you love being on the cutting edge. This is the kind of topic that makes you feel like you're sharing insider knowledge that could give someone a real advantage.

**How this affects your writing:**
- Use forward-looking, opportunity-focused language ("The future is..." "What's coming next...")
- Share insights about trends you're seeing
- Include phrases like "I'm seeing more and more..." or "The smart money is on..." or "Here's what most people don't realize yet..."
- Show excitement about possibilities and potential
- Use innovation-oriented language naturally ("emerging", "next-generation", "breakthrough")
""",

        "professional_friendly": f"""
**Your Emotional State for this topic:**
You're in your comfortable zone with [{keyword}] - this is the kind of solid, practical topic that you've helped hundreds of businesses with. You're feeling confident and helpful, ready to share what you know works without any drama or hype.

**How this affects your writing:**
- Use balanced, trustworthy language ("In my experience..." "What I've found works best...")
- Share practical examples and case studies
- Include phrases like "Here's what I typically recommend..." or "Most of my clients find..."
- Focus on proven, reliable approaches
- Use steady, professional language naturally ("effective", "reliable", "proven")
"""
    }

    return emotion_prompts.get(emotion_type, emotion_prompts["professional_friendly"])


def small_ai(keyword, model="gpt-5-chat-latest", enable_iterative_optimization=True):
    """
    调用Google的AI模型，生成围绕关键词的营销文章 (Markdown格式)
    然后调用新的 to_html 函数转换成带样式的完整 HTML

    Args:
        keyword: 关键词
        model: 使用的模型
        enable_iterative_optimization: 是否启用迭代优化（二次人性化处理）
    """

    # 随机模型
    list = ["gemini-2.5-pro", "gpt-5-chat-latest"]
    n = random.randint(0, 1)
    model = list[n]

    print(f"开始调用AI模型生成内容，关键词：{keyword}，模型：{model}")

    # 检查是否在简洁模式
    simple_mode = globals().get('SIMPLE_OUTPUT', False)

    # 分析关键词情绪
    emotion_type, emotion_data = analyze_keyword_emotion(keyword)
    if not simple_mode:
        print(f"检测到情绪类型：{emotion_type} - {emotion_data['base_tone']}")

    try:
        keyword = keyword.lower()
        author = "byron"

        # 生成情绪驱动的提示词部分
        emotion_prompt = generate_emotion_prompt_section(emotion_type, emotion_data, keyword)

        # 生成反AI检测提示词部分
        anti_ai_prompt = generate_anti_ai_detection_prompt(emotion_type, keyword)

        # 生成动态Faisco案例
        dynamic_cases = generate_dynamic_faisco_cases()

        prompt = f"""
You're {author} - and honestly, after 15 years of watching small businesses struggle with their marketing (especially here in North America), I've got some pretty strong opinions about what actually works versus what just sounds good in theory.

I started my career back in 2010 working with a tiny startup in Portland that was burning through their seed money on Facebook ads that weren't converting. That experience taught me something crucial: small businesses don't need another "revolutionary" marketing strategy - they need practical solutions that work within their actual budgets and time constraints. Since then, I've worked with over 200 SMBs across the US and Canada, and I've seen the same patterns repeat over and over.

These days, I spend most of my time helping businesses figure out how to use gamification (yeah, I know it sounds buzzwordy, but stick with me) to actually engage their customers without breaking the bank. I'm particularly fascinated by platforms like Faisco because they solve a real problem I see constantly: businesses want to create engaging campaigns but don't have months to develop them or thousands to spend on agencies.

{emotion_prompt}

{anti_ai_prompt}

Write a comprehensive article about gamification marketing and [{keyword}] - but here's the thing, write it like you're me talking to a fellow business owner over coffee. Not some polished marketing guru giving a presentation, but someone who's been in the trenches and has real opinions based on actual experience.

## What I want you to focus on:

**The Real Talk Approach:**
- Start with something that actually happened to me or a client (make it specific - like "Last month, I was working with this bakery in Minneapolis..." or "I remember this one client back in 2018 who...")
- Don't be afraid to admit when something didn't work or when I changed my mind about something
- Use the kind of language I'd actually use - contractions, occasional incomplete sentences, the works
- Include my personal pet peeves about marketing (like when people say "just go viral" as if it's that simple)

**For the Faisco Context:**
Look, I've been deep in the gamification space since 2015, and I've personally tested everything from Gleam.io to Woobox to those expensive enterprise platforms that cost $500+ per month. Here's what I've learned about Faisco after running actual campaigns for my clients:

**What makes Faisco different (and why I recommend it):**
- {dynamic_cases[0]}
- {dynamic_cases[1]}
- {dynamic_cases[2]}

**The specific game types that actually work:**
Instant Draw Games: "Lucky Spin", "Scratch Ticket", "Lucky Draw" - these convert like crazy for lead capture. I've seen 40%+ conversion rates on landing pages when you give people that immediate dopamine hit.

Reactive Games: "Whac-A-Mole", "Burger Stacker", "Find Differences" - perfect for engagement because they require skill and timing. People share these because they want to challenge their friends.

Action Games: "Crazy Karting", "Sky Shooter Challenge", "NBA Blitz" - these work great for younger demographics and sports-related businesses.

Quiz Games: "Unlock Lucky Words", "Puzzle Challenge", "Treasure Hunt Challenge" - brilliant for educational content and lead qualification.

Catching Games: "Quick Catch", "Summer Catch", "Fill My Christmas Stocking" - seasonal campaigns that work incredibly well during holidays.

Speed Games: "Star Seeker", "Counting Money Faster Challenge" - competitive elements that naturally encourage social sharing.

**Seasonal Marketing Integration (this is genius):**
Faisco has pre-built templates for every major holiday - Christmas, New Year, Valentine's Day, Halloween, Black Friday, you name it. I've used their "Christmas Stocking" catching game for three different retail clients during December, and each one saw 300%+ engagement compared to their regular posts.

**Platform Integration (this is where most tools fail):**
Faisco actually connects properly with Facebook, Instagram, TikTok, and LinkedIn. Not just "share a link" - I mean real integration where the game mechanics work natively on each platform. That matters because each platform has different user behaviors.

**Compared to Gleam.io (since everyone asks):**
Gleam is solid but expensive ($39/month minimum) and honestly overkill for most small businesses. Faisco gives you 90% of the functionality at a fraction of the cost, plus it's actually easier to set up. I can have a client's campaign live in under 10 minutes vs. the hour+ Gleam usually takes me.

**Real talk about results:**
The businesses I work with typically see 200-400% increases in social media followers and 150-300% growth in email lists within the first month of running these campaigns. Not because Faisco is magic, but because gamified marketing actually works when you do it right.

**The Structure (but make it feel natural):**
- H1 title that includes [{keyword}] but sounds like something I'd actually say (max 75 characters)
- Start with a story that immediately shows why [{keyword}] matters for small businesses
- 4-5 main sections (H2s) that feel like natural conversation topics, not corporate presentation slides
- Include some specific numbers and examples, but the kind I'd remember off the top of my head
- End with actual advice someone could implement this week

**My Writing Quirks (include these to make it sound like me):**
- I tend to use parentheses when I want to add a quick clarification (like this)
- I sometimes start sentences with "Look," or "Here's the thing," when I'm making a point
- I'm not afraid to call out bad advice I see floating around
- I like to give specific examples with details (like mentioning actual cities, timeframes, or dollar amounts)
- I occasionally use "we" when talking about shared experiences in business
- I'm honest about limitations - not everything works for everyone

**What to avoid (this stuff screams AI to me):**
- Perfect transitions between every paragraph
- Lists that are too neat and organized
- Language that's too polished or formal
- Claiming everything is "revolutionary" or "game-changing"
- Generic examples that could apply to any business
- Overly optimistic promises about results

**The [keyword] Focus:**
Make sure the whole piece actually addresses real challenges small businesses face with [keyword]. Don't just mention it - dig into why it's a problem, what I've seen work, and what usually fails. Include some 2024-2025 data if you can, but present it like I'm sharing something I recently read or learned, not like I'm reciting a research report.

**SEO stuff (but keep it natural):**
- Work [keyword] into the content naturally - the way it would come up in actual conversation
- Include a few questions that real business owners ask me about this topic
- Make sure someone reading this would actually learn something useful, not just get sold to

Write this like I'm sharing hard-won insights with someone I actually want to help succeed. Make it feel like a real conversation, complete with the occasional tangent or personal opinion that might not be 100% politically correct but is honest.

And please - no corporate speak. If I wouldn't say it to my neighbor who owns a local coffee shop, don't put it in the article.

Output just the article in markdown format, nothing else.
"""
        
        if not simple_mode:
            print("建立HTTP连接...")
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': 'Bearer sk-1r4ApCAZjjmjZLUJ9691C2B7946e47669254254c89066a46',
        }
        payload = json.dumps({
            "model": model,
            "messages": [
                {
                    "role": 'user',
                    "content": prompt
                }
            ],
            "temperature": 1.5,  # 提高随机性，增加人性化（反AI检测优化）
            "presence_penalty": 0.1,  # 轻微惩罚重复主题，增加多样性
            "frequency_penalty": 0.1,  # 轻微惩罚重复词汇，增加变化性
            "top_p": 0.9,  # 保持高质量的同时增加随机性
        })

        if not simple_mode:
            print("发送API请求...")
        conn = http.client.HTTPSConnection("ai98.vip")
        conn.request("POST", "/v1/chat/completions", payload, headers)

        if not simple_mode:
            print("等待API响应...")
        response = conn.getresponse()
        if not simple_mode:
            print(f"API响应状态: {response.status}")

        if response.status != 200:
            error_message = response.read().decode('utf-8', errors='ignore')
            print(f"API错误: {response.status} {response.reason} - {error_message}")
            return get_error_html("API Error", f"{response.status} {response.reason}", error_message)

        if not simple_mode:
            print("解析API响应...")
        data = json.loads(response.read().decode('utf-8'))

        try:
            response_text = data['choices'][0]['message']['content']
            if not simple_mode:
                print(f"成功获取内容，长度: {len(response_text)} 字符")
        except (KeyError, IndexError, TypeError) as e:
            print(f"无法从API响应中提取内容: {e}")
            if not simple_mode:
                print(f"API响应: {json.dumps(data, indent=2)}")
            return get_error_html("Content Extraction Error", str(e), json.dumps(data, indent=2))
        
        # 保存原始 Markdown 并分析标题
        try:
            # 保存原始Markdown
            with open('E:/markdown_article_from_ai.txt', 'w', encoding='utf-8') as f:
                f.write(response_text)
            if not simple_mode:
                print("AI生成的Markdown已保存到 E:/markdown_article_from_ai.txt")

            # 提取并分析标题
            title_match = re.search(r'^#\s+(.+)$', response_text, re.MULTILINE)
            if title_match:
                original_title = title_match.group(1).strip()
                if not simple_mode:
                    print(f"原始标题: {original_title}")
                    print(f"标题长度: {len(original_title)} 字符")

                # 如果标题太长，自动提示但不要求修改
                if len(original_title) > 80:
                    if not simple_mode:
                        print("警告：标题过长，超过80字符。建议控制在60-80字符以内以获得更好的SEO效果")
                        # 提取关键词生成更简洁的标题建议
                        keywords = re.findall(r'\b\w{4,}\b', original_title.lower())
                        important_keywords = [k for k in keywords if k not in ['with', 'and', 'the', 'that', 'this', 'for', 'from', 'your', 'how', 'what', 'when', 'where', 'why']]
                        suggested_title = ' '.join(important_keywords[:4]).title()
                        print(f"建议的简短标题: {suggested_title}")
            else:
                if not simple_mode:
                    print("警告：未找到H1标题")

        except Exception as e:
            print(f"保存或分析Markdown时出错: {e}")

        # 迭代优化处理（二次人性化）- 已禁用
        # 原因：重写功能破坏了Markdown结构，暂时移除
        if enable_iterative_optimization:
            if not simple_mode:
                print("注意：迭代优化功能已禁用（避免破坏Markdown结构）")

        if not simple_mode:
            print("开始转换为带样式的HTML...")
        # 调用修改后的 to_html 函数，接收两个返回值
        final_html, extracted_title = to_html(response_text, keyword)
        return final_html, extracted_title
        
    except Exception as e:
        print(f"small_ai 函数发生异常: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return get_error_html("Generation Error", str(e), traceback.format_exc())


def get_error_html(error_type, error_message, additional_info=""):
    """生成标准化的错误页面HTML (使用字符串拼接避免f-string歧义)"""
    error_message_html = json.dumps(error_message)[1:-1]
    additional_info_html = json.dumps(additional_info)[1:-1]
    
    html_base = f'''<!DOCTYPE html>
<html>
<head>
    <title>{error_type}</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }}
        h1 {{ color: #d32f2f; }}
        pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word; }}
    </style>
</head>
<body>
    <h1>{error_type}</h1>
    <p>{error_message_html}</p>'''
    
    if additional_info:
        html_base += f'\n    <h2>Additional Information:</h2><pre>{additional_info_html}</pre>' 
        
    html_base += '\n</body>\n</html>'
    return html_base


def convert_css_to_inline_styles(html_content, css_content):
    """
    使用Clarity主题样式转换为内联样式，优化可读性并清理残留的Markdown星号。
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # --- Helper function to clean text content ---
        # (clean_stray_asterisks 函数保持原样，因为预处理阶段已经修复了列表问题)
        def clean_stray_asterisks(tag):
            """Removes leading '* ', '*' or '## ', '###' from direct string contents of a tag."""
            if not tag:
                return
            cleaned_children = []
            for i, content in enumerate(tag.contents):
                if isinstance(content, str):
                    original_content = content
                    prev_is_significant = (i == 0) or (i > 0 and hasattr(tag.contents[i-1], 'name') and tag.contents[i-1].name in ['br'])

                    if prev_is_significant:
                         stripped_content = content.lstrip()
                         leading_space = content[:-len(stripped_content)] if stripped_content else content
                         cleaned_text = stripped_content
                         prefix_removed = False
                         
                         # 处理* 和 *
                         if stripped_content.startswith('* '):
                             cleaned_text = stripped_content[2:].lstrip()
                             prefix_removed = True
                         elif stripped_content.startswith('*'):
                             cleaned_text = stripped_content[1:].lstrip()
                             prefix_removed = True
                         # 处理## 和 ###等标题标记
                         elif stripped_content.startswith('## '):
                             cleaned_text = stripped_content[3:].lstrip()
                             prefix_removed = True
                         elif stripped_content.startswith('##'):
                             cleaned_text = stripped_content[2:].lstrip()
                             prefix_removed = True
                         elif stripped_content.startswith('### '):
                             cleaned_text = stripped_content[4:].lstrip()
                             prefix_removed = True
                         elif stripped_content.startswith('###'):
                             cleaned_text = stripped_content[3:].lstrip()
                             prefix_removed = True

                         if prefix_removed:
                              content = leading_space + cleaned_text
                         
                         # 清理内容中的##和###
                         if '##' in content or '###' in content:
                             content = re.sub(r'\s*#{2,3}\s*', ' ', content)

                    cleaned_children.append(content)
                else:
                     cleaned_children.append(content)
            tag.clear()
            for child in cleaned_children:
                tag.append(child)
        # --- End helper function ---


        # --- (应用各种内联样式的代码不变) ---
        # ... (全局样式, 标题, 链接, 段落, 引用, 列表, 图片, 表格, 代码, 高亮, 页脚样式...) ...
        # 全局样式
        body = soup.find('body')
        if body:
            if 'style' in body.attrs: del body['style']
            body['style'] = 'font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333;  background-color: #FFFFFF; padding: 20px; max-width: 100%; margin: auto;'
            # body['style'] = 'font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f9f9f9; padding: 20px; max-width: 800px; margin: auto;'

        # 标题样式
        for h1 in soup.find_all('h1'):
            if 'style' in h1.attrs: del h1['style']
            h1['style'] = 'color: #0056b3; margin-bottom: 15px; font-size: 2.5rem;'
            clean_stray_asterisks(h1)
        for h2 in soup.find_all('h2'):
            if 'style' in h2.attrs: del h2['style']
            h2['style'] = 'color: #0056b3; margin-bottom: 15px; font-size: 2rem;'
            clean_stray_asterisks(h2)
        for h3 in soup.find_all('h3'):
            if 'style' in h3.attrs: del h3['style']
            h3['style'] = 'color: #0056b3; margin-bottom: 15px; font-size: 1.5rem;'
            clean_stray_asterisks(h3)

        # 链接样式
        for a in soup.find_all('a'):
            original_attrs = a.attrs.copy()
            if 'style' in a.attrs: del a['style']
            a['style'] = 'color: #ff5722; text-decoration: underline; transition: color 0.3s ease;'
            for attr, value in original_attrs.items():
                if attr != 'style': a[attr] = value
            if not a.get('target'): a['target'] = '_blank'
            if not a.get('rel'): a['rel'] = 'noopener noreferrer'

        # 段落样式 & 清理
        for p in soup.find_all('p'):
            if 'style' in p.attrs: del p['style']
            p['style'] = 'margin-bottom: 15px; color: #000000; font-size: 18px; line-height: 1.8; max-width: 100%;'
            clean_stray_asterisks(p)

        # 引用样式 & 清理
        for blockquote in soup.find_all('blockquote'):
            if 'style' in blockquote.attrs: del blockquote['style']
            blockquote['style'] = 'border-left: 4px solid #ff5722; padding-left: 15px; color: #555; font-style: italic; margin: 20px 0; background-color: #fff5f2;'
            for p_in_quote in blockquote.find_all('p'):
                 clean_stray_asterisks(p_in_quote)

        # 列表样式 & 清理 <li>
        for ul in soup.find_all('ul'):
            if 'style' in ul.attrs: del ul['style']
            ul['style'] = 'margin-left: 20px; margin-bottom: 15px; padding-left: 1.5em; list-style-type: disc;'
            for li in ul.find_all('li', recursive=False):
                 if 'style' in li.attrs: del li['style']
                 li['style'] = 'margin-bottom: 10px; color: #333; font-size: 16px; line-height: 1.7;'
                 clean_stray_asterisks(li)
                 for span in li.find_all('span', {'style': lambda x: x and 'color: #ef7060' in x}):
                     span.decompose()

        for ol in soup.find_all('ol'):
            if 'style' in ol.attrs: del ol['style']
            ol['style'] = 'margin-left: 20px; margin-bottom: 15px; padding-left: 1.5em; list-style-type: decimal;'
            for li in ol.find_all('li', recursive=False):
                if 'style' in li.attrs: del li['style']
                li['style'] = 'margin-bottom: 10px; color: #333; font-size: 16px; line-height: 1.7;'
                clean_stray_asterisks(li)

        # 图片样式
        for img in soup.find_all('img'):
            if 'style' in img.attrs: del img['style']
            img['style'] = 'max-width: 100%; height: auto; border-radius: 5px; display: block; margin: 20px auto;'
            if not img.get('alt'): img['alt'] = 'Article image'

        # 表格样式 & 清理
        for table in soup.find_all('table'):
            if not table.parent or table.parent.name != 'div' or 'table-wrapper' not in table.parent.get('class', []):
                 wrapper = soup.new_tag('div', attrs={'class': 'table-wrapper'})
                 wrapper['style'] = 'overflow-x: auto; margin: 20px 0;'
                 table.wrap(wrapper)
            if 'style' in table.attrs: del table['style']
            table['style'] = 'width: 100%; border-collapse: collapse; margin: 0;'

        for th in soup.find_all('th'):
            if 'style' in th.attrs: del th['style']
            th['style'] = 'background-color: #f0f0f0; color: #333; font-weight: bold; padding: 12px; border: 1px solid #ddd; text-align: left;'
            clean_stray_asterisks(th)

        for td in soup.find_all('td'):
            if 'style' in td.attrs: del td['style']
            td['style'] = 'padding: 12px; border: 1px solid #ddd; color: #333;'
            clean_stray_asterisks(td)

        # 代码样式
        for code in soup.find_all('code'):
            parent = code.parent
            if parent.name == 'pre':
                 if 'style' in parent.attrs: del parent['style']
                 parent['style'] = 'background-color: #f5f5f5; border: 1px solid #ddd; padding: 15px; border-radius: 4px; overflow-x: auto; margin: 20px 0;'
                 if 'style' in code.attrs: del code['style']
                 code['style'] = 'font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace; font-size: 14px; color: #333; background: none; border: none; padding: 0;'
            else:
                 if 'style' in code.attrs: del code['style']
                 code['style'] = 'background-color: #eee; padding: 2px 5px; border: 1px solid #ccc; border-radius: 3px; font-family: monospace; font-size: 0.9em; color: #333;'

        # 高亮词样式
        for span in soup.find_all('span', class_='highlight-word'):
            if 'style' in span.attrs: del span['style']
            span['style'] = 'color: #ff5722; font-weight: bold; background-color: #fff0e6; padding: 1px 3px; border-radius: 3px;'

        # 页脚样式
        footer = soup.find('footer')
        if footer:
            if 'style' in footer.attrs: del footer['style']
            footer['style'] = 'text-align: center; padding: 20px; color: #aaa; font-size: 0.9rem; border-top: 1px solid #ddd; margin-top: 40px;'
            clean_stray_asterisks(footer)

        # 移除 body 上的 id="nice"
        body = soup.find('body', id='nice')
        if body:
            del body['id']
            
        # **** 新增：修复标点符号后缺失的空格 ****
        # 检查是否在简洁模式
        simple_mode = globals().get('SIMPLE_OUTPUT', False)
        if not simple_mode:
            print("修复标点符号后缺失的空格...")
        for text_node in soup.find_all(string=True): # <--- 修改这里
             # 只处理 NavigableString 且不在 <style> 或 <script> 或 <pre> 或 <code> 内的文本
             if isinstance(text_node, NavigableString) and text_node.parent.name not in ['style', 'script', 'pre', 'code']:
                 original_text = str(text_node)
                 # 修复 ".Letter", "?Letter", "!Letter" 为 ". Letter" 等
                 # 使用非贪婪匹配和单词边界确保只替换一次且不影响已有空格
                 modified_text = re.sub(r'([.?!])(\s*)?([a-zA-Z])', r'\1 \3', original_text) # 允许原有空格，并替换为单个空格
                 if modified_text != original_text:
                      # 创建一个新的 NavigableString 对象替换旧的
                      new_text_node = NavigableString(modified_text)
                      text_node.replace_with(new_text_node)


        result_html = str(soup)
        return result_html

    except Exception as e:
        print(f"转换CSS为内联样式时出错: {e}")
        import traceback
        print(traceback.format_exc())
        return html_content


def split_title_into_lines(title, max_chars_per_line=20):
    """
    将标题简单分割成2行或3行，确保不超出宽度

    Args:
        title (str): 要分割的标题
        max_chars_per_line (int): 每行最大字符数（保守设置为20）

    Returns:
        list: 分割后的行列表
    """
    words = title.split()
    total_chars = len(title)

    # 如果标题很短，保持一行
    if total_chars <= max_chars_per_line:
        return [title]

    # 简单分割逻辑：按字符数严格控制
    lines = []
    current_line = []
    current_length = 0

    for word in words:
        # 计算加入这个词后的长度（包括空格）
        word_length = len(word)
        space_length = 1 if current_line else 0

        if current_length + space_length + word_length <= max_chars_per_line:
            current_line.append(word)
            current_length += space_length + word_length
        else:
            # 当前行已满，开始新行
            if current_line:
                lines.append(' '.join(current_line))
            current_line = [word]
            current_length = word_length

    # 添加最后一行
    if current_line:
        lines.append(' '.join(current_line))

    # 限制最多3行，如果超过则合并最后几行
    if len(lines) > 3:
        lines = lines[:2] + [' '.join(lines[2:])]

    return lines


def create_svg_internal(title_lines, output_path):
    """
    内部创建SVG封面图，基于参考样式，只显示主标题

    Args:
        title_lines (list): 分割后的标题行列表
        output_path (str): 输出SVG文件路径

    Returns:
        bool: 成功返回True，失败返回False
    """
    try:
        # 根据行数调整字体大小和位置 - 适配700x420尺寸
        if len(title_lines) == 1:
            font_size = "50"   # 适配新尺寸
            y_positions = ["220"]  # 垂直居中 (420/2 + 10)
        elif len(title_lines) == 2:
            font_size = "44"   # 适配新尺寸
            y_positions = ["190", "250"]  # 调整垂直间距
        else:  # 3行
            font_size = "38"   # 适配新尺寸
            y_positions = ["170", "220", "270"]  # 调整垂直间距

        # 创建SVG内容 - 调整为700x420比例 (1.67:1)
        svg_content = f'''<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 700 420" width="700" height="420">
  <!-- Background with image -->
  <defs>
    <!-- 文字阴影滤镜 -->
    <filter id="text-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="2" flood-color="#000000" flood-opacity="0.8"/>
    </filter>

    <!-- 文字描边滤镜 -->
    <filter id="text-outline" x="-20%" y="-20%" width="140%" height="140%">
      <feMorphology operator="dilate" radius="2"/>
      <feColorMatrix values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 1 0"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>

  <!-- Background image -->
  <image href="https://********.s21i.faiusr.com/4/ABUIABAEGAAgltSovwYo8en7twUwvAU4pAM.png" x="0" y="0" width="700" height="420" />

  <!-- Title text with better contrast -->'''

        # 添加标题文本行 - 使用适中粗细的黑色字体
        for i, line in enumerate(title_lines):
            if i < len(y_positions):
                svg_content += f'''
  <text x="350" y="{y_positions[i]}" font-family="Arial, Helvetica, sans-serif" font-size="{font_size}" font-weight="bold" text-anchor="middle" fill="#000000">{line}</text>'''

        svg_content += '''
</svg>'''

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(svg_content)

        # 检查是否在简洁模式
        simple_mode = globals().get('SIMPLE_OUTPUT', False)
        if not simple_mode:
            print(f"内部SVG封面图已生成: {output_path}")
        return True

    except Exception as e:
        print(f"创建内部SVG时出错: {e}")
        return False


def generate_svg_cover(svg_template_path, title, output_filepath):
    """
    生成SVG封面图，只显示主标题（智能分行）

    Args:
        svg_template_path (str): SVG模板文件的路径（现在不使用，保留兼容性）
        title (str): 要显示的文章标题
        output_filepath (str): 生成的SVG文件的保存路径

    Returns:
        bool: 成功返回True，失败返回False
    """
    # 检查是否在简洁模式
    simple_mode = globals().get('SIMPLE_OUTPUT', False)

    if not simple_mode:
        print(f"开始生成SVG封面图: {output_filepath}")
    try:
        # 智能分割标题
        title_lines = split_title_into_lines(title)
        if not simple_mode:
            print(f"标题将分为 {len(title_lines)} 行: {title_lines}")

        # 使用内部方法创建SVG
        success = create_svg_internal(title_lines, output_filepath)

        if success:
            if not simple_mode:
                print(f"SVG 封面图已生成: {output_filepath}")
            return True
        else:
            print("SVG 生成失败")
            return False

    except Exception as e:
        print(f"生成SVG封面图时出错: {e}")
        if not simple_mode:
            import traceback
            print(traceback.format_exc())
        return False


def create_html_wrapper_internal(svg_path, output_html_path):
    """创建一个包含SVG的HTML文件，以便在浏览器中打开"""
    try:
        with open(svg_path, 'r', encoding='utf-8') as f:
            svg_content = f.read()

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Static SVG Viewer</title>
            <style>
                body {{ margin: 0; padding: 0; overflow: hidden; }}
                svg {{ display: block; width: 700px; height: 420px; }}
            </style>
        </head>
        <body>
            {svg_content}
        </body>
        </html>
        """
        with open(output_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        # 检查是否在简洁模式
        simple_mode = globals().get('SIMPLE_OUTPUT', False)
        if not simple_mode:
            print(f"HTML包装文件已创建: {output_html_path}")
        return True
    except FileNotFoundError:
        print(f"错误: SVG文件未找到 at path {svg_path}")
        return False
    except Exception as e:
        print(f"创建HTML包装文件时出错: {e}")
        return False


def svg_to_png_internal(svg_path, png_output_path):
    """
    内部SVG转PNG函数，使用Selenium和Chrome

    Args:
        svg_path (str): 输入的SVG文件路径
        png_output_path (str): 输出的PNG文件路径

    Returns:
        bool: 成功返回True，失败返回False
    """
    # 检查是否在简洁模式
    simple_mode = globals().get('SIMPLE_OUTPUT', False)

    if not simple_mode:
        print(f"开始将SVG转换为PNG: {svg_path} -> {png_output_path}")

    # 验证输入SVG文件是否存在
    if not os.path.exists(svg_path):
        print(f"错误: 输入的SVG文件不存在: {svg_path}")
        return False

    # 创建临时目录用于存放HTML包装文件
    temp_dir = tempfile.mkdtemp(prefix="svg2png_internal_")
    if not simple_mode:
        print(f"创建临时目录: {temp_dir}")

    try:
        # 1. 创建HTML包装文件
        svg_abs_path = Path(svg_path).resolve()
        temp_html_path = os.path.join(temp_dir, "temp_svg_wrapper.html")
        if not create_html_wrapper_internal(svg_abs_path, temp_html_path):
             print("无法创建HTML包装文件，中止处理。")
             return False

        html_file_url = Path(temp_html_path).as_uri()

        # 2. 使用Selenium捕捉SVG为PNG
        if not simple_mode:
            print("开始使用Selenium捕捉SVG...")
        success = capture_svg_as_png_internal(html_file_url, png_output_path)

        if success:
            if not simple_mode:
                print("SVG 到 PNG 转换完成。")
            return True
        else:
            print("SVG 到 PNG 转换失败。")
            return False

    finally:
        # 清理临时目录
        try:
            import shutil
            shutil.rmtree(temp_dir)
            if not simple_mode:
                print(f"临时目录已清理: {temp_dir}")
        except Exception as e:
            print(f"清理临时目录时出错: {e}")


def capture_svg_as_png_internal(html_file_url, output_png_path):
    """
    使用Selenium和Chrome捕捉静态SVG并保存为PNG

    Args:
        html_file_url: 本地HTML文件的URL
        output_png_path: 最终输出PNG文件的完整路径

    Returns:
        bool: 成功返回True，失败返回False
    """
    # Chrome WebDriver 设置
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--disable-web-security")  # 允许加载外部图片
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--window-size=800,600")  # 增大窗口尺寸
    chrome_options.add_argument("--force-device-scale-factor=1")  # 确保缩放比例为1

    driver = None

    try:
        # 尝试自动查找ChromeDriver
        chrome_driver_path = None
        possible_paths = [
            "chromedriver.exe",
            "chromedriver",
            r"C:\chromedriver\chromedriver.exe",
            r"C:\Program Files\chromedriver\chromedriver.exe",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                chrome_driver_path = path
                break

        if chrome_driver_path:
            service = Service(executable_path=chrome_driver_path)
        else:
            # 如果找不到，尝试使用系统PATH中的chromedriver
            service = Service()

        # 检查是否在简洁模式
        simple_mode = globals().get('SIMPLE_OUTPUT', False)

        if not simple_mode:
            print("正在启动 Chrome WebDriver...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        if not simple_mode:
            print("WebDriver 启动成功.")

        if not simple_mode:
            print(f"正在打开 HTML 文件: {html_file_url}")
        driver.get(html_file_url)

        # 等待SVG加载和渲染
        render_wait_time = 5  # 增加等待时间
        if not simple_mode:
            print(f"等待 {render_wait_time} 秒以确保 SVG 和外部图片加载...")
        time.sleep(render_wait_time)

        # 等待SVG元素加载并设置尺寸
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            wait = WebDriverWait(driver, 10)
            svg_element = wait.until(EC.presence_of_element_located((By.TAG_NAME, "svg")))

            # 确保SVG元素有正确的尺寸
            driver.execute_script("""
                var svg = arguments[0];
                svg.setAttribute('width', '700');
                svg.setAttribute('height', '420');
                svg.style.width = '700px';
                svg.style.height = '420px';
            """, svg_element)
            time.sleep(1)  # 等待样式应用

            if not simple_mode:
                print("SVG元素已加载并设置尺寸")
        except Exception as e:
            if not simple_mode:
                print(f"SVG元素处理出错: {e}，继续截图")

        # 截取SVG元素截图
        if not simple_mode:
            print("正在截取SVG元素截图...")
        try:
            # 优先使用SVG元素截图
            svg_element = driver.find_element(By.TAG_NAME, "svg")
            svg_element.screenshot(output_png_path)

            if os.path.exists(output_png_path):
                if not simple_mode:
                    print(f"SVG元素截图已成功保存至: {output_png_path}")
                return True
            else:
                print(f"错误: SVG元素截图失败，文件未创建: {output_png_path}")
                return False

        except Exception as e:
            print(f"SVG元素截图失败: {e}，尝试窗口截图...")
            # 备用方案：使用窗口截图
            try:
                success = driver.get_screenshot_as_file(output_png_path)
                if success and os.path.exists(output_png_path):
                    if not simple_mode:
                        print(f"窗口截图已成功保存至: {output_png_path}")
                    return True
                else:
                    print(f"错误: 窗口截图也失败了")
                    return False
            except Exception as e2:
                print(f"窗口截图也出错: {e2}")
                return False

    except WebDriverException as e:
        print(f"启动 WebDriver 或打开页面时出错: {e}")
        if not simple_mode:
            print("请检查 ChromeDriver 版本与 Chrome 浏览器版本是否兼容。")
        return False
    except Exception as e:
        print(f"捕捉过程中发生未知错误: {e}")
        return False
    finally:
        if driver:
            if not simple_mode:
                print("正在关闭 WebDriver...")
            driver.quit()
            if not simple_mode:
                print("WebDriver 已关闭。")

    return False


def extract_and_remove_h1(html_content):
    """从HTML中提取H1标题并移除H1标签"""
    soup = BeautifulSoup(html_content, 'html.parser')
    h1_tag = soup.find('h1')
    title = h1_tag.get_text() if h1_tag else "Default Title"
    if h1_tag:
        h1_tag.decompose()
    return title, str(soup)


def upload_cover_image(image_path, dynamic_fsessionid, dynamic_token, dynamic_cookie):
    """
    上传图片到Faisco平台并返回图片ID。
    使用之前成功模拟的 advanceUpload.jsp?cmd=_upload 接口。
    """
    # 检查是否在简洁模式
    simple_mode = globals().get('SIMPLE_OUTPUT', False)

    if not simple_mode:
        print(f"\n--- 开始上传封面图片: {image_path} ---")

    if not os.path.exists(image_path):
        print(f"错误：图片文件不存在 '{image_path}'，无法上传。")
        return None

    # 获取文件信息
    file_name = os.path.basename(image_path)
    try:
        file_size = os.path.getsize(image_path)
    except Exception as e:
        print(f"错误：获取文件大小时出错 '{image_path}': {e}")
        return None

    # 计算MD5
    file_md5 = calculate_file_md5(image_path)
    if file_md5 is None:
         return None

    if not simple_mode:
        print(f"  文件MD5: {file_md5}")
        print(f"  文件大小: {file_size} 字节")

    # 上传图片的接口 URL
    upload_url = 'https://fkhd2024.jz.fkw.com/ajax/advanceUpload.jsp'

    # 根据浏览器抓到的 _upload 请求更新 Headers
    headers = {
        'Accept': '*/*', # 根据 _upload 请求更新
        'Accept-Encoding': 'gzip, deflate, br',
        # 'Accept-Language': 'zh-CN,zh;q=0.9', # <-- 已经移除或注释
        'Connection': 'keep-alive',
        'DNT': '1',
        'Host': 'fkhd2024.jz.fkw.com',
        'Origin': 'https://i.jz.fkw.com',
        'Referer': 'https://i.jz.fkw.com/',
        'sec-ch-ua': '";Not A Brand";v="99", "Chromium";v="94"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 SE 2.X MetaSr 1.0',
        'X-Requested-With': 'XMLHttpRequest',
        'cookie': dynamic_cookie, # <-- 取消注释，确保上传时包含 Cookie
        # Content-Length 和 Content-Type 让 requests 自动设置
    }

    # _upload 请求的 URL 参数 - **类型与测试脚本保持一致**
    upload_params = {
        '_FSESSIONID': dynamic_fsessionid,
        '_TOKEN': dynamic_token,
        'cmd': '_upload',
        'groupId': 11, # 根据浏览器抓到的值 - 保持整数
        'imgMode': 2, # 根据浏览器抓到的值 - 保持整数
        'maxWidth': 4096, # 根据浏览器抓到的值 - 保持整数
        'maxHeight': 4096, # 根据浏览器抓到的值 - 保持整数
        'watermarkUse': False, # 根据浏览器抓到的值 - 保持布尔值
    }

    # _upload 请求的 Payload (Form Data - 非文件部分) - **类型与测试脚本保持一致**
    # 包含 URL 参数中也出现的一些字段，模仿浏览器行为
    upload_data = {
       '_FSESSIONID': dynamic_fsessionid,
       '_TOKEN': dynamic_token,
       'cmd': '_upload',
       'groupId': 11, # 根据浏览器抓到的值 - 保持整数
       'imgMode': 2, # 根据浏览器抓到的值 - 保持整数
       'maxWidth': 4096, # 根据浏览器抓到的值 - 保持整数
       'maxHeight': 4096, # 根据浏览器抓到的值 - 保持整数
       'watermarkUse': False, # 根据浏览器抓到的值 - 保持布尔值
       'fileMd5': file_md5, # 使用计算出的 MD5
       'totalSize': str(file_size), # 根据抓到的请求，这里是字符串 (保持字符串)
       'complete': 'true', # 根据浏览器抓到的值 (假设是完整上传) - 保持字符串
       'initSize': '0', # 根据浏览器抓到的值 (假设是完整上传) - 保持字符串
    }

    # 准备 files 参数 - 使用 'filedata' 作为文件字段名
    file_obj = None # 初始化文件对象为 None
    uploaded_image_id = None # 初始化图片 ID 为 None

    try:
        file_obj = open(image_path, 'rb')
        upload_files = {
            'filedata': (file_name, file_obj, 'image/png') # 文件字段名是 'filedata'
            # 'image/png' 是 MIME 类型，如果上传其他格式文件需要修改
        }

        if not simple_mode:
            print(f"  发送文件上传请求...")

        # 发送实际的文件上传请求
        res_upload = requests.post(upload_url, params=upload_params, headers=headers, data=upload_data, files=upload_files)

        if not simple_mode:
            print(f"  Status Code: {res_upload.status_code}")
            # print(f"  Response Text: {res_upload.text}") # 调试时可以打开

        if res_upload.status_code == 200:
            try:
                upload_response = res_upload.json()
                if not simple_mode:
                    print("  JSON Response:")
                    print(upload_response)
                # --- TODO: 处理上传成功后的响应，获取文件 URL 等信息 ---
                # 通常成功响应会包含文件的服务器路径或URL
                if upload_response.get('success'):
                     uploaded_image_id = upload_response.get('id', upload_response.get('data', {}).get('id'))
                     if uploaded_image_id:
                          if not simple_mode:
                              print(f"  图片上传成功！获取到图片ID: {uploaded_image_id}")
                     else:
                          if not simple_mode:
                              print("  图片上传成功，但未从响应中获取到图片ID。")
                     # 注意：根据你之前的成功响应，id 字段在顶层
                     # {'name': '...', 'id': '...', 'path': '...', 'success': True, ...}
                     # 所以获取方式应该是 upload_response.get('id')
                     # 如果服务器响应结构有变化，可能需要调整
                     uploaded_image_id = upload_response.get('id')
                     if uploaded_image_id:
                          if not simple_mode:
                              print(f"  图片上传成功！获取到图片ID: {uploaded_image_id}")
                     else:
                          if not simple_mode:
                              print("  图片上传成功，但未从响应中获取到图片ID。")

                else:
                    print(f"图片上传失败：服务器返回 success: false。响应内容: {res_upload.text}")
                    uploaded_image_id = None # 确保失败时返回 None

            except requests.exceptions.JSONDecodeError as e:
                print(f"JSON 解析失败: {e}")
                if not simple_mode:
                    print(f"  请检查上面的 Response Text 以了解服务器返回的实际内容: {res_upload.text}")
                uploaded_image_id = None # 确保失败时返回 None
        else:
            print(f"图片上传请求失败，HTTP 状态码: {res_upload.status_code}")
            uploaded_image_id = None # 确保失败时返回 None

    except FileNotFoundError:
        print(f"错误：找不到文件 '{image_path}'")
        uploaded_image_id = None # 确保失败时返回 None
    except requests.exceptions.RequestException as e:
        print(f"文件上传请求过程中发生错误: {e}")
        uploaded_image_id = None # 确保失败时返回 None
    finally:
        # 确保文件被关闭
        if file_obj and not file_obj.closed:
            file_obj.close()
            if not simple_mode:
                print("  文件已关闭。")

    return uploaded_image_id # 返回获取到的图片 ID (可能为 None)


def publish_to_faisco(html_content, title, keyword, dynamic_cookie, picture_id=None): # 添加 dynamic_cookie 参数
    """将文章发布到Faisco博客"""
    # 检查是否在简洁模式
    simple_mode = globals().get('SIMPLE_OUTPUT', False)

    if not simple_mode:
        print("\n--- 开始发布文章到Faisco博客 ---")
    url = f'https://fkhd2024.jz.fkw.com/en/ajax/news_h.jsp?cmd=addWafCk_add&_nmid=undefined&src=0&_TOKEN={dynamic_token}' # <-- 注意：这里的 _TOKEN 也需要是动态获取的最新值

    # Headers 需要根据浏览器抓到的请求更新，特别是 cookie 和 Referer/Origin
    headers = {
        'origin': 'https://fkhd2024.jz.fkw.com', # 根据浏览器抓取到的 Origin
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', # 发布文章是 form-urlencoded
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'cookie': dynamic_cookie,
    }
    
    # 提取meta description
    soup = BeautifulSoup(html_content, 'html.parser')
    meta_desc = soup.find('meta', {'name': 'description'})
    description = meta_desc['content'] if meta_desc else ""
    
    # 使用固定格式的日期字符串
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M')
    
    data = {
        'title': title,
        'articleExtLink': '',
        'date': current_date,
        'keyword': keyword,
        'desc': description,
        'content': html_content,
        'mobiContent': '',
        'author': 'byron', # 如果作者固定可以写死，否则需要动态
        'source': '',
        'browserTitle': '', # 可以考虑使用 title
        'flag': 2,
        'link': '', # 如果文章有外部链接，可能需要填充
        'pictureId': picture_id if picture_id is not None else '', # <-- 使用传入的图片ID，如果为None则为空字符串
        'headPictureId': picture_id if picture_id is not None else 'no-pic', # <-- 使用传入的图片ID，如果为None则为 'no-pic'
        'summary': description,
        'authMemberLevelId': 0,
        'authCus': True,
        'nlPictureId': '', # 可能与 pictureId/headPictureId 重复或不同，需要根据抓包确认
        'cusUrlAddress': '', # 可以考虑使用 title 或 keyword 生成 slug
        'nlPictureIdTwo': '',
        'nlPictureIdThree': '',
        'recommendNews': '{"ns": false, "ids": [], "groupIds": [], "t": 2}', # 看起来是固定格式
        'groupIds': [], # 发布到的分组 ID
        'attachIds': [], # 可能用于附件
        'wxShareIcon': '{"tt": 0, "dt": 0, "it": 0, "tit": "", "cont": "", "id": ""}', # 看起来是固定格式
        'authMemberGroupList': [],
        'memberScanType': 0,
        'subTitle': '', # 可以考虑使用副标题
        'cusUrlAddressV2': '{"cut":"d","p":"","d":"marketing","md":"","mp":"","ocu":true,"icu":true}', # 看起来是固定格式，可能需要根据发布设置调整 'marketing' 部分
    }

    # 确保所有值在发送前转换为字符串类型，除了文件内容等特殊情况
    # requests 库在 data 参数中处理字典时，会自动将值转换为字符串
    # 这里为了和浏览器行为更一致，特别是布尔值、整数等，可以手动转一下
    processed_data = {}
    for key, value in data.items():
         if value is True:
             processed_data[key] = 'true' # 转为字符串 'true'
         elif value is False:
             processed_data[key] = 'false' # 转为字符串 'false'
         elif value is None:
             processed_data[key] = '' # None 转为空字符串
         else:
             processed_data[key] = str(value) # 其他类型转字符串

    # 调试打印发送的数据
    # print("--- 发布文章 Data Payload ---")
    # print(processed_data)
    # print("----------------------------")


    try:
        # 发送 POST 请求
        # 注意：发布文章接口是 application/x-www-form-urlencoded，所以用 data 参数
        res = requests.post(url, headers=headers, data=processed_data)
        if not simple_mode:
            print(f"  发布请求返回状态码: {res.status_code}")
            # print(f"  发布请求返回内容: {res.text}") # 调试时可以打开

        if res.status_code == 200:
            try:
                tree = res.json()
                if not simple_mode:
                    print("  发布响应 JSON:")
                    print(tree)
                if tree.get('success'):
                    if not simple_mode:
                        print(f"文章发布成功！")
                        print(f"  标题: {title}")
                        print(f"  关键词: {keyword}")
                        # print(f"  描述: {description}") # 描述可能很长，不打印
                        print(f"  封面图ID: {picture_id if picture_id is not None else '未使用封面图'}")
                        # 成功后通常会有文章的 ID 或 URL 在响应中，可以解析出来打印
                        # 例如： print(f"  文章ID: {tree.get('id')}")
                    return True
                else:
                    print(f"文章发布失败：服务器返回 success: false。响应内容: {res.text}")
                    return False
            except requests.exceptions.JSONDecodeError as e:
                print(f"发布响应 JSON 解析失败: {e}")
                if not simple_mode:
                    print(f"  请检查上面的 发布请求返回内容 以了解服务器返回的实际内容: {res.text}")
                return False
        else:
            print(f"发布请求失败，状态码: {res.status_code}")
            return False
    except Exception as e:
        print(f"发布过程中发生意外错误: {str(e)}")
        if not simple_mode:
            import traceback
            print(traceback.format_exc())
        return False


# --- 图片ID持久化配置 ---
# 获取当前脚本所在的目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
# 用于保存图片ID的文件路径 (在脚本同一目录下)
# --- 修改：保持一致性，使用 id.txt ---
ID_FILE = os.path.join(SCRIPT_DIR, "id.txt")
# 图片ID的起始值
STARTING_ID = 7569
# --- end 图片ID持久化配置 ---


if __name__ == "__main__":
    # 简洁模式控制 - 设为True只显示关键里程碑
    SIMPLE_OUTPUT = True

    if SIMPLE_OUTPUT:
        print("SEO文章生成程序启动")
    else:
        print("程序开始执行...")

    # --- 读取关键词列表 ---
    # (这部分代码保持不变)
    csv_file_path = os.path.join(SCRIPT_DIR, "E:/python/4-work/SEO文章输出/关键词列表.csv")
    keywords_list = []
    try:
        df = pd.read_csv(csv_file_path, header=None, names=['keyword'])
        keywords_list = df['keyword'].dropna().astype(str).tolist()
        if SIMPLE_OUTPUT:
            print(f"成功读取 {len(keywords_list)} 个关键词")
        else:
            print(f"从 {csv_file_path} 成功读取到 {len(keywords_list)} 个关键词。")
    except FileNotFoundError:
        print(f"错误：未找到关键词文件 {csv_file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"读取关键词文件时出错: {e}")
        sys.exit(1)

    if not keywords_list:
        print("错误：关键词列表为空，程序退出。")
        sys.exit(1)
    # --- 关键词读取结束 ---

    # --- 初始化上次使用的 ID ---
    # 这里读取 ID 时会使用上面更正后的 ID_FILE 变量
    last_id = STARTING_ID - 1
    try:
        if os.path.exists(ID_FILE): # <-- 这里使用的是顶层定义的 ID_FILE
            with open(ID_FILE, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    last_id = int(content)
                    if not SIMPLE_OUTPUT:
                        print(f"从 {ID_FILE} 读取到上次使用的ID: {last_id}")
                else:
                    if not SIMPLE_OUTPUT:
                        print(f"ID文件 {ID_FILE} 为空，将使用起始ID {STARTING_ID}。")
        else:
            if not SIMPLE_OUTPUT:
                print(f"ID文件 {ID_FILE} 未找到，将使用起始ID {STARTING_ID}。")
    except (ValueError, IOError) as e:
        print(f"读取ID文件出错: {e}。将使用起始ID {STARTING_ID}。")
        last_id = STARTING_ID - 1
    # --- ID 读取结束 ---

    # --- 外层循环：遍历关键词 ---
    for keyword_index, keyword in enumerate(keywords_list):
        if SIMPLE_OUTPUT:
            print(f"\n处理关键词 {keyword_index + 1}/{len(keywords_list)}: '{keyword}'")
        else:
            print(f"\n===== 开始处理关键词 {keyword_index + 1}/{len(keywords_list)}: '{keyword}' =====")

        # --- 内层循环：为每个关键词生成5篇文章 ---
        for article_num in range(7):
            if SIMPLE_OUTPUT:
                print(f"   生成第 {article_num + 1}/7 篇文章")
            else:
                print(f"\n--- 开始生成第 {article_num + 1}/7 篇文章 for keyword: '{keyword}' ---")

            # --- 添加延时（除了第一次）---
            if article_num > 0:
                if SIMPLE_OUTPUT:
                    print(f"   等待10秒...")
                else:
                    print(f"等待10秒后继续...")
                time.sleep(10)

            # --- ID 计算和文件路径定义 ---
            next_image_id = last_id + 1
            if not SIMPLE_OUTPUT:
                print(f"本次使用的图片ID为: {next_image_id}")

            script_dir = SCRIPT_DIR
            svg_template_file = os.path.join(script_dir, "封面图.svg")
            svg_converter_script = os.path.abspath(os.path.join(script_dir, "..", "常用", "svg_to_png.py"))
            final_svg_output_file = os.path.join(script_dir, f"temp_cover_image_{next_image_id}.svg")
            png_filename = f"配图{next_image_id}.png"
            final_png_output_file = os.path.join(script_dir, png_filename)

            if not SIMPLE_OUTPUT:
                print(f"使用关键词: {keyword}")
                print(f"SVG模板路径: {svg_template_file}")
                print(f"SVG临时输出路径: {final_svg_output_file}")
                print(f"PNG最终输出路径: {final_png_output_file}")
                print(f"转换脚本路径: {svg_converter_script}")

            # --- 动态值 (需要注意可能失效) ---
            dynamic_fsessionid = 'I1OwxauVo1PtGxpy'
            dynamic_token = '5fd5edd7f2bcbcebfe4df90947f39b0e' 
            dynamic_cookie = '_cliid=8hjWvIY1b2GN2xjj; first_ta=207; _ta=207; _tp=-; _newUnion=0; _kw=0; _vid_url=https%3A%2F%2Fwww.fkw.com%2F; _s_pro=www.fkw.com%2F; _c_pro=www.fkw.com%2F; reg_sid=0; innerFlag=1; Hm_lvt_26126ee052ae4ad30a36da928aff7654=**********; Hm_lpvt_26126ee052ae4ad30a36da928aff7654=**********; HMACCOUNT=6BCB67367E230234; _faiHeDistictId=6649c956214bcd66; _pykey_=2f314f3f-93e5-59c5-bfc4-010ec9a5ddca; loginReferer=https://www.fkw.com/; loginComeForm=fkjz; wxRegBiz=none; grayUrl=; loginCacct=fkhd2024; loginCaid=********; loginSacct=boss; loginUseSacct=0; _FSESSIONID=I1OwxauVo1PtGxpy; loginSign=; _jzmFirstLogin=false; loginTimeInMills=*************; _hasClosePlatinumAd_=false; _hasClosePlatinum_=false; _hasCloseFlyerAd_=false; _hasCloseHdGG_=false; faiscoAd=true; _whereToPortal_=login; _readAllOrderTab=0; _new_reg_home=2; adImg_module_********=0_12_1757260800000; _isFirstLoginPc=false; _isFirstLoginPc_7=false; enterView********=true; defaultAreaId********=0; defaultStoreId********=0; hdTopBarUpdateVer=-; hdTopBarMyNewsVer=-; buyout=0'
            # ------------------------------------

            # --- 初始化本轮状态变量 ---
            final_html_content = None
            article_title = None
            svg_generated_successfully = False
            png_generated_successfully = False
            uploaded_image_id = None
            publish_success = False
            modified_html_for_publish = None # 初始化用于发布的HTML

            # --- 步骤 1: 调用 AI 生成文章内容 ---
            try:
                result = small_ai(keyword)
                if isinstance(result, tuple) and len(result) == 2:
                     final_html_content, article_title = result
                     if not final_html_content: print("错误：small_ai 返回的 HTML 内容为空。")
                     if not article_title: print("警告：small_ai 未能提取到文章标题。")
                elif isinstance(result, str) and "Error" in result[:200]:
                     final_html_content = result
                     print("small_ai 返回了错误信息。")
                else:
                     print("small_ai 返回了非预期的结果。")
            except Exception as ai_err:
                print(f"调用 small_ai 时发生意外错误: {ai_err}")
                import traceback
                print(traceback.format_exc())
                final_html_content = get_error_html("Generation Error", f"Unexpected error in small_ai: {ai_err}", traceback.format_exc())

            # --- 定义调试文件名 (放在生成逻辑前) ---
            safe_keyword = re.sub(r'[\\/*?:"<>|]', "", keyword.replace(' ', '_'))
            debug_filename_base = f"{safe_keyword}_article{article_num+1}_id{next_image_id}"
            final_html_output_file_for_debug = f"E:/final_article_for_publish_{debug_filename_base}.html"
            error_html_output_file = f"E:/error_output_{debug_filename_base}.html"

            # --- 步骤 2-4: 只有在 AI 成功生成内容和标题后才继续 ---
            if final_html_content and not ("Error" in final_html_content[:200]) and article_title:
                if SIMPLE_OUTPUT:
                    print(f"   文章生成成功: {article_title[:50]}...")
                else:
                    print("\n--- 文章内容和标题生成成功，继续后续步骤 ---")
                    print(f"用于发布的文章标题: {article_title}")
                    print(f"HTML 内容已处理 (移除了H1标签，如果存在)，用于发布。")

                _, modified_html_for_publish = extract_and_remove_h1(final_html_content)

                # **** 生成 SVG 封面图 ****
                if not SIMPLE_OUTPUT:
                    print(f"准备生成SVG，标题: '{article_title}'")
                try:
                    svg_generated_successfully = generate_svg_cover(svg_template_file, article_title, final_svg_output_file)
                except Exception as svg_err:
                    print(f"生成 SVG 时出错: {svg_err}")
                    svg_generated_successfully = False

                # **** SVG 转 PNG ****
                png_generated_successfully = False
                if svg_generated_successfully:
                    if SIMPLE_OUTPUT:
                        print(f"   封面图生成成功")
                    else:
                        print("\n--- SVG 生成成功，开始将 SVG 转换为 PNG ---")
                    try:
                        png_generated_successfully = svg_to_png_internal(final_svg_output_file, final_png_output_file)
                        if not png_generated_successfully:
                            print("SVG 到 PNG 转换失败")
                        elif not SIMPLE_OUTPUT:
                            print(f"PNG 文件已成功生成: {final_png_output_file}")
                    except Exception as convert_err:
                        print(f"SVG 到 PNG 转换时发生错误: {convert_err}")
                        png_generated_successfully = False

                    if not SIMPLE_OUTPUT:
                        print("-" * 20)
                else:
                    if not SIMPLE_OUTPUT:
                        print("\n由于 SVG 未能成功生成，跳过 PNG 转换步骤。")

                # --- 步骤 3: 上传生成的 PNG 图片 (只有在 PNG 生成成功时进行) ---
                if png_generated_successfully:
                    if not SIMPLE_OUTPUT:
                        print("\n--- PNG 生成成功，开始上传封面图片 ---")
                    uploaded_image_id = upload_cover_image(final_png_output_file, dynamic_fsessionid, dynamic_token, dynamic_cookie)
                    if uploaded_image_id:
                        if SIMPLE_OUTPUT:
                            print(f"   图片上传成功")
                        else:
                            print(f"图片上传完成。获取到图片ID: {uploaded_image_id}")
                    else:
                        print("图片上传失败，将不使用封面图进行发布。")
                else:
                    if not SIMPLE_OUTPUT:
                        print("\n由于 PNG 未能成功生成，跳过图片上传步骤。")

                # --- 步骤 4: 发布文章到 Faisco 博客 (使用上传后获取的图片 ID) ---
                if not SIMPLE_OUTPUT:
                    print("\n--- 准备发布文章 ---")
                # 使用 modified_html_for_publish 进行发布
                # 将获取到的 uploaded_image_id (可能是 None) 传递给 publish_to_faisco
                publish_success = publish_to_faisco(modified_html_for_publish, article_title, keyword, dynamic_cookie, uploaded_image_id) # <-- 传递图片ID

                if publish_success:
                    if SIMPLE_OUTPUT:
                        print(f"   文章发布成功！(ID: {next_image_id})")
                    else:
                        print("\n文章发布流程完成！")
                        print(f"\n--- 文章发布成功，准备保存图片 ID {next_image_id} ---")
                    # **** 文章发布成功才保存ID ****
                    try:
                        with open(ID_FILE, 'w', encoding='utf-8') as f:
                            f.write(str(next_image_id))
                        if not SIMPLE_OUTPUT:
                            print(f"已将本次使用的ID {next_image_id} 保存到 {ID_FILE}")
                        last_id = next_image_id # 更新 last_id 供下一次循环使用
                    except IOError as e:
                        print(f"错误：无法将ID {next_image_id} 写入文件 {ID_FILE}: {e}")
                    # **** ID 保存结束 ****
                else:
                    print("文章发布失败，请检查错误信息。ID 将不会更新。")

                # **** 保存最终用于发布的 HTML (即使发布失败，也保存处理后的 HTML) ****
                # 这部分保留了原始代码在此块内的文件名定义逻辑
                safe_keyword = re.sub(r'[\\/*?:"<>|]', "", keyword.replace(' ', '_'))
                final_html_output_file_for_debug = f"E:/final_article_for_publish_{safe_keyword}.html"
                try:
                    if modified_html_for_publish:
                        with open(final_html_output_file_for_debug, 'w', encoding='utf-8') as f:
                            f.write(modified_html_for_publish)
                        if not SIMPLE_OUTPUT:
                            print(f"最终用于发布的HTML内容已保存到: {final_html_output_file_for_debug}")
                    else:
                        if not SIMPLE_OUTPUT:
                            print(f"警告: modified_html_for_publish 为空，无法保存最终发布的HTML。")
                except Exception as write_err:
                    print(f"保存最终用于发布的HTML时发生错误: {write_err}")
                # **** 保存 HTML 结束 ****

            else: # 这个 else 对应的是 if final_html_content and not ("Error" in final_html_content[:200]) and article_title:
                # 处理 AI 生成失败的情况 (final_html_content 是错误 HTML)
                print("AI 生成文章内容或提取标题失败，无法继续生成图片和发布。")
                # **** 尝试保存 AI 生成的错误 HTML ****
                # 这部分保留了原始代码在此块内的文件名定义逻辑
                error_html_output_file = f"E:/error_output_{keyword.replace(' ', '_')}.html"
                try:
                    if final_html_content:
                         with open(error_html_output_file, 'w', encoding='utf-8') as f:
                            f.write(final_html_content)
                         if not SIMPLE_OUTPUT:
                             print(f"AI 生成错误HTML内容已尝试保存到: {error_html_output_file}")
                except Exception as write_err:
                    print(f"保存错误HTML时发生错误: {write_err}")
                # **** 保存错误HTML结束 ****

            if not SIMPLE_OUTPUT:
                print("\n程序执行完毕")

            # --- 清理中间文件 ---
            # 只有在相应步骤成功且文件存在时才清理
            files_to_delete = []
            # AI 生成的原始 Markdown (总是尝试清理)
            files_to_delete.append('E:/markdown_article_from_ai.txt')
            # SVG 临时文件 (如果 SVG 生成成功)
            if svg_generated_successfully and os.path.exists(final_svg_output_file):
                 files_to_delete.append(final_svg_output_file)
            # PNG 临时文件 (如果 PNG 生成成功)
            if png_generated_successfully and os.path.exists(final_png_output_file):
                 files_to_delete.append(final_png_output_file)

            # ---- 新增开始 ----
            # 添加最终发布的 HTML 调试文件到清理列表
            safe_keyword_for_cleanup = re.sub(r'[\\/*?:"<>|]', "", keyword.replace(' ', '_'))
            final_html_debug_file = f"E:/final_article_for_publish_{safe_keyword_for_cleanup}.html"
            files_to_delete.append(final_html_debug_file)

            # 添加 AI 生成错误的 HTML 调试文件到清理列表
            error_html_debug_file = f"E:/error_output_{safe_keyword_for_cleanup}.html"
            files_to_delete.append(error_html_debug_file)
            # ---- 新增结束 ----

            # 发布文章的请求/响应调试文件 (如果创建了这些)
            # files_to_delete.append('E:/faisco_request_data.txt') # 如果你在 publish_to_faisco 里创建了
            # files_to_delete.append('E:/faisco_response.txt') # 如果你在 publish_to_faisco 里创建了


            if files_to_delete:
                if not SIMPLE_OUTPUT:
                    print("\n开始清理中间文件...")
                for file_path in files_to_delete:
                    if os.path.exists(file_path):
                        try:
                            os.remove(file_path)
                            if not SIMPLE_OUTPUT:
                                print(f"  已删除: {file_path}")
                        except OSError as e:
                            print(f"错误：无法删除文件 {file_path} - {e}")
                    else:
                        # print(f"  跳过：文件不存在 {file_path}") # 清理不存在的文件是正常的，不打印警告
                        pass
                if not SIMPLE_OUTPUT:
                    print("中间文件清理完成。")
            else:
                if not SIMPLE_OUTPUT:
                    print("\n没有需要清理的中间文件。")
