# Keye多模态大语言模型部署指南

> 🐾 由Python开发猫娘制作的完整部署方案

## 🎯 部署目标
在 E:\python 目录下部署 Kwai Keye-VL 多模态大语言模型，支持图像和视频理解功能。

## 📋 系统要求
- Python 3.8+（已有Python 3.11 ✅）
- CUDA支持的GPU（推荐16GB+显存）
- 磁盘空间：至少50GB可用空间
- 网络：稳定的互联网连接（下载模型文件）

## 🚀 阶段1：环境准备

### 1.1 创建项目目录
```bash
# 激活虚拟环境
E:\python\.venv\Scripts\activate

# 创建项目目录
mkdir E:\python\keye
cd E:\python\keye

# 创建子目录
mkdir models
mkdir cache
mkdir examples
mkdir logs
```

### 1.2 配置环境变量
```bash
# 设置HuggingFace缓存目录（避免下载到C盘）
set HF_HOME=E:\python\keye\cache
set TRANSFORMERS_CACHE=E:\python\keye\cache
set HF_HUB_CACHE=E:\python\keye\cache

# 设置视频处理参数
set VIDEO_MAX_PIXELS=896000
```

### 1.3 克隆项目代码
```bash
git clone https://github.com/Kwai-Keye/Keye.git
cd Keye
```

## 🔧 阶段2：依赖安装

### 2.1 安装基础依赖
```bash
# 安装PyTorch（CUDA版本）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 安装transformers和相关依赖
pip install transformers>=4.37.0
pip install accelerate
pip install pillow
pip install requests
pip install packaging
pip install av
```

### 2.2 安装Keye工具包
```bash
# 安装keye-vl-utils
pip install keye-vl-utils

# 可选：安装视频处理增强包
pip install decord
```

### 2.3 验证安装
```python
# 创建测试脚本：test_installation.py
import torch
import transformers
from keye_vl_utils import process_vision_info

print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
print(f"Transformers版本: {transformers.__version__}")
print("keye-vl-utils导入成功!")
```

## 📦 阶段3：模型下载

### 3.1 选择模型版本
- **Keye-VL-8B-Preview**: 预览版本，相对稳定
- **Keye-VL-1.5-8B**: 最新版本，功能更强

### 3.2 下载模型（推荐Preview版本开始）
```python
# 创建下载脚本：download_model.py
from transformers import AutoModel, AutoProcessor
import os

# 设置缓存目录
os.environ['HF_HOME'] = 'E:\\python\\keye\\cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:\\python\\keye\\cache'

model_path = "Kwai-Keye/Keye-VL-8B-Preview"

print("开始下载模型...")
try:
    # 下载处理器
    processor = AutoProcessor.from_pretrained(
        model_path, 
        trust_remote_code=True,
        cache_dir="E:\\python\\keye\\cache"
    )
    print("处理器下载完成!")
    
    # 下载模型（这会需要一些时间）
    model = AutoModel.from_pretrained(
        model_path,
        torch_dtype="auto",
        trust_remote_code=True,
        cache_dir="E:\\python\\keye\\cache"
    )
    print("模型下载完成!")
    
except Exception as e:
    print(f"下载出错: {e}")
    print("请检查网络连接或尝试使用镜像源")
```

## 🧪 阶段4：功能测试

### 4.1 基础推理测试
```python
# 创建测试脚本：test_basic_inference.py
from transformers import AutoModel, AutoProcessor
from keye_vl_utils import process_vision_info
import torch

# 加载模型
model_path = "Kwai-Keye/Keye-VL-8B-Preview"
model = AutoModel.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto",
    trust_remote_code=True,
)

processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)

# 测试文本生成
messages = [
    {
        "role": "user",
        "content": [
            {"type": "text", "text": "你好，请介绍一下你自己。"}
        ],
    }
]

text = processor.apply_chat_template(
    messages, tokenize=False, add_generation_prompt=True
)
image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
inputs = processor(
    text=[text],
    images=image_inputs,
    videos=video_inputs,
    padding=True,
    return_tensors="pt",
    **mm_processor_kwargs
)

if torch.cuda.is_available():
    inputs = inputs.to("cuda")

# 生成回复
generated_ids = model.generate(**inputs, max_new_tokens=512)
generated_ids_trimmed = [
    out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
]
output_text = processor.batch_decode(
    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
)
print("模型回复:", output_text[0])
```

### 4.2 图像理解测试
```python
# 创建图像测试脚本：test_image_understanding.py
from transformers import AutoModel, AutoProcessor
from keye_vl_utils import process_vision_info
from PIL import Image
import requests
import torch

# 加载模型
model_path = "Kwai-Keye/Keye-VL-8B-Preview"
model = AutoModel.from_pretrained(
    model_path,
    torch_dtype="auto",
    device_map="auto",
    trust_remote_code=True,
)
processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)

# 使用网络图片测试
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "image",
                "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
            },
            {"type": "text", "text": "请详细描述这张图片的内容。"},
        ],
    }
]

# 处理输入
text = processor.apply_chat_template(
    messages, tokenize=False, add_generation_prompt=True
)
image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
inputs = processor(
    text=[text],
    images=image_inputs,
    videos=video_inputs,
    padding=True,
    return_tensors="pt",
    **mm_processor_kwargs
)

if torch.cuda.is_available():
    inputs = inputs.to("cuda")

# 生成描述
generated_ids = model.generate(**inputs, max_new_tokens=1024)
generated_ids_trimmed = [
    out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
]
output_text = processor.batch_decode(
    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
)
print("图像描述:", output_text[0])
```

## ⚡ 阶段5：高性能部署（可选）

### 5.1 安装vLLM
```bash
# 安装vLLM进行高性能推理
pip install keye-vl-utils "vllm>=0.9.2"
```

### 5.2 vLLM推理测试
```python
# 创建vLLM测试脚本：test_vllm_inference.py
from transformers import AutoProcessor
from vllm import LLM, SamplingParams
from keye_vl_utils import process_vision_info

model_path = "Kwai-Keye/Keye-VL-8B-Preview"

# 初始化vLLM
llm = LLM(
    model=model_path,
    limit_mm_per_prompt={"image": 10, "video": 10},
    trust_remote_code=True,
)

sampling_params = SamplingParams(
    temperature=0.3,
    max_tokens=256,
)

# 测试消息
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "image",
                "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
            },
            {"type": "text", "text": "这张图片展示了什么？"},
        ],
    },
]

processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
prompt = processor.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True,
)
image_inputs, video_inputs, video_kwargs = process_vision_info(
    messages, return_video_kwargs=True
)

mm_data = {}
if image_inputs is not None:
    mm_data["image"] = image_inputs

llm_inputs = {
    "prompt": prompt,
    "multi_modal_data": mm_data,
    "mm_processor_kwargs": video_kwargs,
}

outputs = llm.generate([llm_inputs], sampling_params=sampling_params)
generated_text = outputs[0].outputs[0].text
print("vLLM生成结果:", generated_text)
```

## 🔧 常见问题解决

### GPU内存不足
```python
# 使用CPU推理或量化
model = AutoModel.from_pretrained(
    model_path,
    torch_dtype=torch.float16,  # 使用半精度
    device_map="cpu",  # 强制使用CPU
    trust_remote_code=True,
)
```

### 网络下载问题
```bash
# 使用镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple keye-vl-utils

# 设置HuggingFace镜像
set HF_ENDPOINT=https://hf-mirror.com
```

## 📝 使用建议

1. **首次使用**：建议从基础推理开始，确认环境正常
2. **图像处理**：支持本地文件、URL、base64等多种输入格式
3. **视频处理**：需要更多GPU内存，建议先测试图像功能
4. **性能优化**：生产环境建议使用vLLM进行部署

## 🎉 部署完成检查清单

- [ ] 虚拟环境激活
- [ ] 项目目录创建
- [ ] 环境变量设置
- [ ] 基础依赖安装
- [ ] keye-vl-utils安装
- [ ] 模型下载完成
- [ ] 基础推理测试通过
- [ ] 图像理解测试通过
- [ ] （可选）vLLM安装和测试

完成以上步骤后，您就可以开始使用Keye多模态大语言模型了！
