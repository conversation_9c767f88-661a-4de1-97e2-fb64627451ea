#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye-VL 快速测试脚本
作者: Python猫娘 🐾
功能: 验证安装并进行简单测试
"""

def test_imports():
    """测试导入"""
    print("🔍 测试1: 检查包导入")
    try:
        from transformers import AutoModel, AutoProcessor
        print("✅ transformers 导入成功")
        
        from keye_vl_utils import process_vision_info
        print("✅ keye_vl_utils 导入成功")
        
        import torch
        print(f"✅ torch 导入成功 (版本: {torch.__version__})")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA设备: {torch.cuda.get_device_name()}")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🔍 测试2: 模型加载测试")
    print("⚠️  这将下载约8GB的模型文件，确认继续吗？")
    
    choice = input("输入 'y' 继续，其他键跳过: ").strip().lower()
    if choice != 'y':
        print("⏭️  跳过模型加载测试")
        return True
    
    try:
        from transformers import AutoModel, AutoProcessor
        
        model_path = "Kwai-Keye/Keye-VL-1.5-8B"
        print(f"🚀 正在加载模型: {model_path}")
        print("⏳ 首次运行需要下载模型文件，请耐心等待...")
        
        # 加载处理器（较小，先测试）
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        print("✅ 处理器加载成功")
        
        # 加载模型（较大）
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
            trust_remote_code=True,
        )
        print("✅ 模型加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_simple_inference():
    """测试简单推理"""
    print("\n🔍 测试3: 简单推理测试")
    
    try:
        from transformers import AutoModel, AutoProcessor
        from keye_vl_utils import process_vision_info
        
        model_path = "Kwai-Keye/Keye-VL-1.5-8B"
        
        # 加载模型和处理器
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
            trust_remote_code=True,
        )
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        
        # 简单的文本对话测试
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Hello! Can you introduce yourself?"},
                ],
            }
        ]
        
        # 处理输入
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            **mm_processor_kwargs
        )
        
        # 移动到设备
        device = next(model.parameters()).device
        inputs = inputs.to(device)
        
        # 生成回答
        print("🤔 正在生成回答...")
        generated_ids = model.generate(**inputs, max_new_tokens=256)
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        print("🎯 模型回答:")
        print(output_text[0])
        print("✅ 推理测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🐾 Keye-VL 安装验证和测试")
    print("=" * 40)
    
    # 测试1: 导入检查
    if not test_imports():
        print("\n❌ 基础导入测试失败，请检查安装")
        return
    
    # 测试2: 模型加载
    if not test_model_loading():
        print("\n❌ 模型加载测试失败")
        return
    
    # 测试3: 简单推理
    if not test_simple_inference():
        print("\n❌ 推理测试失败")
        return
    
    print("\n🎉 所有测试通过！Keye-VL 安装成功并可以正常使用")
    print("\n📋 下一步:")
    print("1. 运行 python keye_demo.py 查看完整示例")
    print("2. 开始使用Keye-VL进行多模态AI对话")
    print("3. 参考官方文档: https://github.com/Kwai-Keye/Keye")

if __name__ == "__main__":
    main()
