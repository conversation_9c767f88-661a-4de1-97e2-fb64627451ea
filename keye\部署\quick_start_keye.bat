@echo off
chcp 65001 >nul
echo 🐾 Keye多模态大语言模型快速部署脚本
echo ==========================================
echo.

echo 🔍 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未找到，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo.
echo 🚀 激活虚拟环境...
call E:\python\.venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo.
echo 📁 设置环境变量...
set HF_HOME=E:\python\keye\cache
set TRANSFORMERS_CACHE=E:\python\keye\cache
set HF_HUB_CACHE=E:\python\keye\cache
set VIDEO_MAX_PIXELS=896000
echo ✅ 环境变量设置完成

echo.
echo 🐍 运行Python部署脚本...
python E:\python\keye\部署\deploy_keye.py

echo.
echo 🎉 快速部署脚本执行完成！
echo 请查看上方输出信息了解部署状态
echo.
pause
