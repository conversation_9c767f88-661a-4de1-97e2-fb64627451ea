import csv
import time

from selenium import webdriver
from selenium.webdriver.common.alert import Alert
from selenium.webdriver.common.by import By


def alert():
    alert = Alert(driver)
    alert.accept()
    time.sleep(1)
    alert.accept()
    time.sleep(1)


with open('E:/aid.csv', 'r', newline='', encoding='utf-8') as f:
    reader = csv.reader(f)
    d = []
    for k in reader:
        d.append(str(k[0]))
print(d)
## time.sleep(1)

# 前置输入要下订单的信息，包括合作方，账号名称，购买版本，购买年限
dd = 12
partner = '客服内部账号'
url = 'http://o.fkw.com.faidev.cc/'
acct = ''

for acct in d:
    # 访问OSS系统并登录
    driver = webdriver.Chrome()  # 设置引擎为Chrome，真实地打开一个Chrome浏览器
    driver.get(url)  # 访问页面
    account = driver.find_element(By.ID, 'account')
    pd = driver.find_element(By.ID, 'password')
    account.send_keys('byron')
    pd.send_keys('3292128900202a')
    time.sleep(1)
    button_login = driver.find_element(By.CLASS_NAME, 'login_btn')
    button_login.click()
    time.sleep(1)

    # 进入特殊订单下单页面
    driver.get(f'{url}/index.jsp?t=cs&u=/cs/orderAdd.jsp?acct=faisco&aid=1&type=0')
    time.sleep(2)
    # 查询需要下单的账号，并将获取到的aid保存下来
    driver.switch_to.frame('tframe')
    x_acct = driver.find_element(By.XPATH, "/html/body/div[1]/div[1]/div[2]/input")
    button_check = driver.find_element(By.XPATH, "/html/body/div[1]/div[1]/div[2]/button")
    x_acct.clear()
    x_acct.send_keys(acct)
    time.sleep(2)
    button_check.click()
    time.sleep(2)
    aid = driver.find_element(By.XPATH, "//div[@class='search-box']//div[2]//input[1]").get_attribute('value')
    # 选择互动业务里的对应版本
    hd = driver.find_element(By.XPATH,"/html/body/div/div[2]/div[3]/div/label[18]/span[1]/input")
    hd.click()
    time.sleep(1)
    path = "//span[text()='速创专业版']"
    x_version = driver.find_element(By.XPATH, path)
    x_version.click()
    time.sleep(1)

    # 判断账号是否开通互动，未开通时自动点击开通
    try:
        button_kt = driver.find_element(By.XPATH, "/html/body/div/div[2]/div[5]/div/div/div/div/span/button")
        button_kt.click()
        time.sleep(1)
        button_yes = driver.find_element(By.XPATH, "/html/body/div[3]/div/div[2]/div/div[2]/div/div/div[2]/button[2]")
        button_yes.click()
        time.sleep(2)
    except:
        pass

    # 下单，填上购买月份（年份*12），客户备注，点击提交
    num = driver.find_element(By.XPATH, "/html/body/div/div[2]/div[5]/div/div/div/div/span/div/label[1]/input")
    pick = driver.find_element(By.XPATH, "/html/body/div/div[2]/div[5]/div/div/div/div/span/div/label[3]/input")
    button2 = driver.find_element(By.XPATH,"/html/body/div/div[2]/div[7]/button")
    num.send_keys(dd)
    pick.send_keys(partner)
    time.sleep(1)
    button2.click()
    # 订单提交确认
    time.sleep(2)
    button_ok = driver.find_element(By.XPATH,"/html/body/div[2]/div/div[2]/div/div[2]/div[3]/div/button[1]")
    button_ok.click()
    print("下单成功")

    # 完成订单支付：先访问订单页面，获取到订单明细地址
    driver.get(f'{url}/index.jsp?t=cs&u=/cs/orderSearch.jsp?aid={acct}')
    time.sleep(1)
    driver.switch_to.frame('tframe')
    # order_link = driver.find_element(By.CLASS_NAME, "el-table_1_column_1   el-table__cell").find_element(By.XPATH, "//a").get_attribute("href")
    order_link = driver.find_element(By.XPATH,"/html/body/div[2]/div[1]/div[3]/table/tbody/tr[1]/td[15]/div/a").get_attribute("href")
    time.sleep(1)
    driver.get(order_link)
    time.sleep(1)
    order_id = driver.find_element(By.XPATH, "/html[1]/body[1]/div[2]/div[1]/table[1]/tbody[1]/tr[1]/td[2]").text
    amount = driver.find_element(By.XPATH, "/html/body/div[2]/div[1]/table/tbody/tr[12]/td[2]").text
    # 如果订单未结算的情况下，需要先完成结算
    try:
        settle = driver.find_element(By.LINK_TEXT, "结算")
        settle.click()
        time.sleep(1)
        # 处理alert弹窗确认
        alert()
    except:
        pass
    # 选择支付方式：合作伙伴
    check = driver.find_element(By.LINK_TEXT, "修改支付方式")
    check.click()
    time.sleep(1)
    # 处理alert弹窗确认
    alert()
    # 添加现金券
    driver.get(f'{url}/cs/orderCouponAdd.jsp?aid={acct}&id={order_id}')
    num = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/form/div[4]/div[2]/div/span/input")
    num.clear()
    num.send_keys(amount)
    div = driver.find_element(By.XPATH,
                              "//body/div[@id='main']/div/form[@class='fa-form fa-form-horizontal']/div[5]/div[2]/div["
                              "1]/span[1]/div[1]/div[1]")
    div.click()
    time.sleep(1)
    discount_type = driver.find_element(By.XPATH, "//body//li[8]")  # 选择自定义选项
    discount_type.click()
    time.sleep(1)
    label = driver.find_element(By.XPATH, "//input[@placeholder='120字内']")
    label.send_keys(partner)
    time.sleep(1)
    button_check = driver.find_element(By.XPATH, "//button[@class='fa-btn fa-btn-primary']")
    button_check.click()
    time.sleep(1)
    
    # 完成支付
    driver.get(order_link)
    check = driver.find_element(By.LINK_TEXT, "完成支付")
    check.click()
    # 处理alert弹窗确认
    alert()
    # 判断订单是否处理完成
    order_status = driver.find_element(By.XPATH, "/html/body/div[2]/div[1]/table/tbody/tr[13]/td[2]").text
    if order_status == "处理完成":
        print("支付成功")
        print(f"账号为{acct}, 订单号为：{order_id}")
    else:
        print("处理异常")
    
    # 关闭浏览器
    driver.quit()
