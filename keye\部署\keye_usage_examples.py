#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye多模态大语言模型使用示例
作者：Python开发猫娘 🐾
"""

import os
import torch
from transformers import AutoModel, AutoProcessor
from keye_vl_utils import process_vision_info
from PIL import Image
import requests
from io import BytesIO

class KeyeDemo:
    def __init__(self, model_name="Kwai-Keye/Keye-VL-8B-Preview"):
        """初始化Keye模型"""
        print("🐾 初始化Keye多模态模型...")
        
        # 设置缓存目录
        os.environ['HF_HOME'] = 'E:\\python\\keye\\cache'
        os.environ['TRANSFORMERS_CACHE'] = 'E:\\python\\keye\\cache'
        
        self.model_name = model_name
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        
        try:
            # 加载模型和处理器
            self.model = AutoModel.from_pretrained(
                model_name,
                torch_dtype="auto",
                device_map="auto",
                trust_remote_code=True,
            )
            
            self.processor = AutoProcessor.from_pretrained(
                model_name, 
                trust_remote_code=True
            )
            
            print("✅ 模型加载成功！")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def generate_response(self, messages, max_tokens=1024):
        """生成回复"""
        try:
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
            
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
                **mm_processor_kwargs
            )
            
            if self.device == "cuda":
                inputs = inputs.to("cuda")
            
            # 生成回复
            with torch.no_grad():
                generated_ids = self.model.generate(
                    **inputs, 
                    max_new_tokens=max_tokens,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9
                )
            
            # 解码输出
            generated_ids_trimmed = [
                out_ids[len(in_ids) :] 
                for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            return output_text[0]
            
        except Exception as e:
            return f"生成回复时出错: {e}"
    
    def text_chat(self, question):
        """纯文本对话"""
        print(f"\n🗣️  用户: {question}")
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": question}
                ],
            }
        ]
        
        response = self.generate_response(messages)
        print(f"🤖 Keye: {response}")
        return response
    
    def image_understanding(self, image_path, question="请详细描述这张图片。"):
        """图像理解"""
        print(f"\n🖼️  图像理解任务")
        print(f"图像: {image_path}")
        print(f"问题: {question}")
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": image_path},
                    {"type": "text", "text": question},
                ],
            }
        ]
        
        response = self.generate_response(messages)
        print(f"🤖 Keye: {response}")
        return response
    
    def video_understanding(self, video_path, question="请描述这个视频的内容。"):
        """视频理解"""
        print(f"\n🎥 视频理解任务")
        print(f"视频: {video_path}")
        print(f"问题: {question}")
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "video", "video": video_path},
                    {"type": "text", "text": question},
                ],
            }
        ]
        
        response = self.generate_response(messages, max_tokens=1024)
        print(f"🤖 Keye: {response}")
        return response
    
    def thinking_mode(self, image_path, question):
        """思维模式（更详细的推理）"""
        print(f"\n🧠 思维模式")
        print(f"图像: {image_path}")
        print(f"问题: {question}")
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": image_path},
                    {"type": "text", "text": f"{question}/think"},  # 添加/think触发思维模式
                ],
            }
        ]
        
        response = self.generate_response(messages, max_tokens=2048)
        print(f"🤖 Keye (思维模式): {response}")
        return response

def main():
    """主函数 - 演示各种功能"""
    print("🐾 Keye多模态大语言模型使用示例")
    print("=" * 50)
    
    try:
        # 初始化模型
        keye = KeyeDemo()
        
        # 示例1: 纯文本对话
        print("\n📝 示例1: 纯文本对话")
        keye.text_chat("你好，请介绍一下你自己。")
        
        # 示例2: 图像理解（使用网络图片）
        print("\n📝 示例2: 图像理解")
        image_url = "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg"
        keye.image_understanding(image_url, "这张图片中有什么动物？它在做什么？")
        
        # 示例3: 思维模式
        print("\n📝 示例3: 思维模式（详细推理）")
        keye.thinking_mode(image_url, "分析这张图片的构图和色彩搭配")
        
        # 示例4: 本地图片（如果存在）
        local_image = "E:/python/keye/examples/test_image.jpg"
        if os.path.exists(local_image):
            print("\n📝 示例4: 本地图片分析")
            keye.image_understanding(f"file://{local_image}", "请分析这张图片的内容和特点。")
        
        print("\n🎉 所有示例执行完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        print("请确保模型已正确下载和安装")

def interactive_mode():
    """交互模式"""
    print("🐾 进入Keye交互模式")
    print("输入 'quit' 退出，输入 'help' 查看帮助")
    
    try:
        keye = KeyeDemo()
        
        while True:
            print("\n" + "="*50)
            user_input = input("🗣️  您: ").strip()
            
            if user_input.lower() == 'quit':
                print("👋 再见！")
                break
            elif user_input.lower() == 'help':
                print("""
🐾 Keye交互模式帮助：
- 直接输入文本进行对话
- 输入图片路径 + 问题进行图像理解
- 输入 'quit' 退出
- 输入 'help' 查看此帮助
                """)
                continue
            elif not user_input:
                continue
            
            # 检查是否包含图片路径
            if any(ext in user_input.lower() for ext in ['.jpg', '.png', '.jpeg', 'http']):
                parts = user_input.split(' ', 1)
                if len(parts) == 2:
                    image_path, question = parts
                    keye.image_understanding(image_path, question)
                else:
                    keye.image_understanding(parts[0])
            else:
                keye.text_chat(user_input)
                
    except Exception as e:
        print(f"❌ 交互模式出错: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_mode()
    else:
        main()
