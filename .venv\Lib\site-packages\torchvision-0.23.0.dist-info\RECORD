torchvision-0.23.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.23.0.dist-info/LICENSE,sha256=wGNj-dM2J9xRc7E1IkRMyF-7Rzn2PhbUWH1cChZbWx4,1546
torchvision-0.23.0.dist-info/METADATA,sha256=8-g68Z6uyBlfkg3TYXZlS0VwFUemPtwTk2nVYK5lU7g,6134
torchvision-0.23.0.dist-info/RECORD,,
torchvision-0.23.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.23.0.dist-info/WHEEL,sha256=SAw8ns6kJWJnXnbRE00TtcpGWx44Z3WvjJsDRxwIxr8,101
torchvision-0.23.0.dist-info/top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/_C.pyd,sha256=dYaQ9VCIrUi_f4AygHFWwwcYr_-oA4ZtOqCz8kLvOjo,738816
torchvision/__init__.py,sha256=yUybb2eKRBX2LWsDZPAnEpHWtEFcGHS-Xo5r8K-Ric0,3639
torchvision/__pycache__/__init__.cpython-311.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-311.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-311.pyc,,
torchvision/__pycache__/_utils.cpython-311.pyc,,
torchvision/__pycache__/extension.cpython-311.pyc,,
torchvision/__pycache__/utils.cpython-311.pyc,,
torchvision/__pycache__/version.cpython-311.pyc,,
torchvision/_internally_replaced_utils.py,sha256=LogrBRyA6oM0joW1_kzgVYicV6ZYCFsszHbcuPxgGSA,1458
torchvision/_meta_registrations.py,sha256=_rNIAEDFkHMmgYGm982-WgSmhA9o2KVJpd8DsoUlQmk,7433
torchvision/_utils.py,sha256=8TvBoMxnfm9Ow4ypvjoWxrF7hKwJ2hX5UUxN209zbV8,988
torchvision/datasets/__init__.py,sha256=Ajw_RJcPNYz-iKvkvQEkgw2P341llDsDgd8cke-bxas,3753
torchvision/datasets/__pycache__/__init__.cpython-311.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-311.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-311.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-311.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-311.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-311.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-311.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-311.pyc,,
torchvision/datasets/__pycache__/coco.cpython-311.pyc,,
torchvision/datasets/__pycache__/country211.cpython-311.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-311.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-311.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-311.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-311.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-311.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-311.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-311.pyc,,
torchvision/datasets/__pycache__/folder.cpython-311.pyc,,
torchvision/datasets/__pycache__/food101.cpython-311.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-311.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-311.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-311.pyc,,
torchvision/datasets/__pycache__/imagenette.cpython-311.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-311.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-311.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-311.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-311.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-311.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-311.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-311.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-311.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-311.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-311.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-311.pyc,,
torchvision/datasets/__pycache__/places365.cpython-311.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-311.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-311.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-311.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-311.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-311.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-311.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-311.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-311.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-311.pyc,,
torchvision/datasets/__pycache__/usps.cpython-311.pyc,,
torchvision/datasets/__pycache__/utils.cpython-311.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-311.pyc,,
torchvision/datasets/__pycache__/vision.cpython-311.pyc,,
torchvision/datasets/__pycache__/voc.cpython-311.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-311.pyc,,
torchvision/datasets/_optical_flow.py,sha256=tQOwa1VOvDIr69xWnpFa-eBJ5Kg5oVBM0ceDpXNvSTE,21720
torchvision/datasets/_stereo_matching.py,sha256=i1JemwbjQhsiMLB3Hrmdo4vYpX4FcNjGpdDQAYF_Pdc,50263
torchvision/datasets/caltech.py,sha256=FPIYyRzzhgmQHEgRF6jbm-7luzrWJq6KL0O8MlyYY8Y,9038
torchvision/datasets/celeba.py,sha256=B-bqLqcpRVLHqa9hsMZbhg8J310D4HeeicnqKNOD6OU,8741
torchvision/datasets/cifar.py,sha256=1T71RgltoNVjjWxjCUSRBCQh6Bf_O50Sim8w3cOA40Q,5951
torchvision/datasets/cityscapes.py,sha256=BPT91qlUiaCz7Zve2bhTTlnKUR11TEOgJdex_CPuwwg,10552
torchvision/datasets/clevr.py,sha256=VdRijEXJBsR1ED-z0FjJUj7QVF7tEgmPHwflamXe1DU,3948
torchvision/datasets/coco.py,sha256=KS_Ny3rtnahIZ7vzILgjWCnnupIHnVB6UVYV1pMHnOE,4456
torchvision/datasets/country211.py,sha256=aw2FzdX-OTQtvHFldL7p5CVA02-7Qt6AXX6j4UQEC8M,2956
torchvision/datasets/dtd.py,sha256=QkPVyrfzJBwVfSWjwk2m7Gx8fOxDG8KUsvZJ18lTJ4E,4525
torchvision/datasets/eurosat.py,sha256=vLcnf6XlVIT9MG1ZTPB8kfY10TXqTJcZ6QelpQNmqSE,2832
torchvision/datasets/fakedata.py,sha256=pdSyCMLTLSW0KWIoYgTbPv0rcYFWj8p6J7vjODl7h0o,2507
torchvision/datasets/fer2013.py,sha256=Clw7azQ4RpapE2giqWK-eOUiR-SuYwfOM5ikjaOAYLc,5226
torchvision/datasets/fgvc_aircraft.py,sha256=4RblkmHUiXoxFngtnIL1iDpiHhe29pKGt5f51d7ptPk,5088
torchvision/datasets/flickr.py,sha256=v5zMcyDQcnzCvp7Y4qN6560q_lFSyBB3Poq0S1eFwyI,6346
torchvision/datasets/flowers102.py,sha256=luufpQWTl9rdFTPQheQBbOlzV8edFOaAxnZnDMenpmk,7706
torchvision/datasets/folder.py,sha256=68dCiaX8Pvv8JIiuIhbx0kaVIyp22RyST8zOnxB2YBc,13322
torchvision/datasets/food101.py,sha256=KGA1JCwbXBEbEQjbuyhsiCEldsfFlLahFies35IMkxQ,4243
torchvision/datasets/gtsrb.py,sha256=pERh6_VqsEPJKXYMY1UdiyZTjrl8cp2g6og04ifgIp0,3881
torchvision/datasets/hmdb51.py,sha256=FKxcu2yOGSQVCqZ7kir2VFxzR7h7XjlM7yP-Xiv4oic,6104
torchvision/datasets/imagenet.py,sha256=758dmmmpAm8HN4PZ2Z9sqj6QmyhO6D-bvkWj7eKsIE4,9144
torchvision/datasets/imagenette.py,sha256=LmLm2dq98tv5Znw31tH09cO26VyYZK3YcEJ6hrXALXE,4730
torchvision/datasets/inaturalist.py,sha256=KiWpvP3kBTel1vor198wjZ68X4xv0q83_SNsTb4Eo38,10547
torchvision/datasets/kinetics.py,sha256=ujFVpL3yrbLWwLQs8g0_TS8JWKWzqNN6P_TpBEnqtZo,10102
torchvision/datasets/kitti.py,sha256=XsFC8DaCCMiwvs20Qtyxpltr-7yDj4A1_CSVGO6UdE0,5782
torchvision/datasets/lfw.py,sha256=WT1g-zKyszYDArSTBA-r-nU1yRhLobgevi13GhS0DRA,11671
torchvision/datasets/lsun.py,sha256=EaPp0k9EcCktMoN12dAGbuu92PuxVtEzUOTyycN_RdU,5898
torchvision/datasets/mnist.py,sha256=yhBEtZ2CZqCybEo0fDLcXjL9WGpS8ISy-pqzl7xE0vo,22364
torchvision/datasets/moving_mnist.py,sha256=Cmw-Pj4xEzmfGZab5O0SGHjxYntg7lyL6XCwuzr1FTQ,3738
torchvision/datasets/omniglot.py,sha256=e51DpqaTFrffgrT3g_HoCCB26xqAa6ILo1FQxC_kwTU,4593
torchvision/datasets/oxford_iiit_pet.py,sha256=cl4OoiEw5l2QzjUcl1ZsSOeTcChM1gTR_eURvP2HErs,5831
torchvision/datasets/pcam.py,sha256=QROSVEEcVZ7WP_GNmUBzot49r54jQ2PzhW8cqFuRbrw,5412
torchvision/datasets/phototour.py,sha256=7ZT9Le--HQU87kzWw1qTDL8UZdLcLJDSsyMzcvh_bKk,8085
torchvision/datasets/places365.py,sha256=z7XKs8zVImJvvcrh0nt0FnlrP9Xz1rZ6oEDnSnObMmg,7643
torchvision/datasets/rendered_sst2.py,sha256=FMlBdmva-_oWor7uSgDFH4HlYm8EoE0QyenTPrSALD8,4046
torchvision/datasets/samplers/__init__.py,sha256=yX8XD3h-VwTCoqF9IdQmcuGblZxvXb2EIqc5lPqXXtY,164
torchvision/datasets/samplers/__pycache__/__init__.cpython-311.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-311.pyc,,
torchvision/datasets/samplers/clip_sampler.py,sha256=oBhAHsW7BYEKGnUtJ7EqCYy3yTtJYahofAdSPcKiHtA,6438
torchvision/datasets/sbd.py,sha256=STXgWtZNOJ_P6pj4nYwR9p7HjjPLmLz26b8O5qH_T0Q,5533
torchvision/datasets/sbu.py,sha256=oEQZm7y5Xu_XMT3WN-PGTpy864IT-Y4CWCeLaeitsKI,4578
torchvision/datasets/semeion.py,sha256=klYpsKYYuYAZpLylwp3OQX9HBWcKfyJipTDpW3B47H4,3191
torchvision/datasets/stanford_cars.py,sha256=ezj66uMKHejcdkGY0Im6rhRbRvrGKH_F482Ne-gwLC4,4386
torchvision/datasets/stl10.py,sha256=lWXtiNAbbRQxVMTnHIY74pfl5vZWsjM3jiGIcWFq0Co,7401
torchvision/datasets/sun397.py,sha256=fDAmuyll2fw-Wph6cckmpsar9fm3xtAkjq3a7gRiruQ,3257
torchvision/datasets/svhn.py,sha256=TEzQE4RUgwdH-ezfDib-sdQD1ORpQM6YWn_WJF4zmMM,4951
torchvision/datasets/ucf101.py,sha256=vTTf8KY82qKdd8HuHY1NcM_ZUt0Bu53BOF0eFZc-820,5645
torchvision/datasets/usps.py,sha256=74zymHuzkJenGD_5Gbd2aV_Wsi9R7E-1Xsd-tbr-1vU,3605
torchvision/datasets/utils.py,sha256=F_NX_BoNlomVt-MlFJ0mpud34Nj7IrLN3OjvLZjb1WA,16382
torchvision/datasets/video_utils.py,sha256=rfroJInqA3iFZ7hOgRA7e6mddYcM904bsmZg2oGKf44,17613
torchvision/datasets/vision.py,sha256=s3_TFYSzplp-TSLDW_QNu4z0Xpua48D5iDZjJDQo__I,4347
torchvision/datasets/voc.py,sha256=cFi8zSfU8CSig6dvnQIBEnjEMVqJEE5Jgs6e8ehojnA,9040
torchvision/datasets/widerface.py,sha256=jQpfLZ2by9F8ABigruWY6nDQwMfAv4z63IZQ9XwC43s,8437
torchvision/extension.py,sha256=0A4efQ6V8RlQcMMtDpK74VIBHpDv4icjkkOc-EokPHw,3233
torchvision/image.pyd,sha256=pxDvYxi1u16KEAl8zQHFlU7xLo3xiB9NCJxTPmea_DM,174592
torchvision/io/__init__.py,sha256=gzG3Qr6eYLnwemcJFTmWwL8g4Bfd3K9BRVydqo2EnY4,1656
torchvision/io/__pycache__/__init__.cpython-311.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-311.pyc,,
torchvision/io/__pycache__/_video_deprecation_warning.cpython-311.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-311.pyc,,
torchvision/io/__pycache__/image.cpython-311.pyc,,
torchvision/io/__pycache__/video.cpython-311.pyc,,
torchvision/io/__pycache__/video_reader.cpython-311.pyc,,
torchvision/io/_load_gpu_decoder.py,sha256=vUWhEXG1Dq3pIJHd8avo4o333Z7zx2DN7FOzIxuGluE,186
torchvision/io/_video_deprecation_warning.py,sha256=iQ5WX0Kz-URtrFG5tqc04KSspCHZYUGUDwr3lx0y27s,462
torchvision/io/_video_opt.py,sha256=quiNhMJnXC2oI2_Y-4wRbblPajs6Z5sMT6yEuN76M0g,21304
torchvision/io/image.py,sha256=Dp8aDR5xsw34dt0gHbZq3k9RabYa_CgfT_5vhDyopB8,22223
torchvision/io/video.py,sha256=1TvERcZFZuWEpvt1PxWs65SZmj68PA9Rtf39jL18myM,18806
torchvision/io/video_reader.py,sha256=_myMyJLiiugJtzuHh3HWc-M5l9R-V_kdIT-46dHZMaM,12158
torchvision/jpeg8.dll,sha256=aM-Kj2MkrdHI0gkgpHfh86_icuM26XiMu6gyMGeuKig,552448
torchvision/libjpeg.dll,sha256=85Tk07ezKBsmEjt5H6Yu53aO-ckgPdT6CxG6C-VYyx8,284944
torchvision/libpng16.dll,sha256=nPxu7uIrOxgpEXCLUyjP7rQBCghBVPsL-h8OxeArvc0,192512
torchvision/libsharpyuv.dll,sha256=6Lz4Enzhcx9UiWUBCycwqTHTi_D-35YOM-p8zbwB2QM,34576
torchvision/libwebp.dll,sha256=hN1hG6LP-7hh6DCpbYtX82QTWG0KT2EFtSaX2wrJqOc,389392
torchvision/models/__init__.py,sha256=6QlTJfvjKcUmMJvwSapWUNFXbf2Vo15dVRcBuNSaYko,888
torchvision/models/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/__pycache__/_api.cpython-311.pyc,,
torchvision/models/__pycache__/_meta.cpython-311.pyc,,
torchvision/models/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/__pycache__/alexnet.cpython-311.pyc,,
torchvision/models/__pycache__/convnext.cpython-311.pyc,,
torchvision/models/__pycache__/densenet.cpython-311.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-311.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-311.pyc,,
torchvision/models/__pycache__/googlenet.cpython-311.pyc,,
torchvision/models/__pycache__/inception.cpython-311.pyc,,
torchvision/models/__pycache__/maxvit.cpython-311.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-311.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-311.pyc,,
torchvision/models/__pycache__/regnet.cpython-311.pyc,,
torchvision/models/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-311.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-311.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-311.pyc,,
torchvision/models/__pycache__/vgg.cpython-311.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-311.pyc,,
torchvision/models/_api.py,sha256=yv71Vl6qY5Dnvh7PtiTSfsq94TIpEjNHcFnpH3pR7VM,10253
torchvision/models/_meta.py,sha256=2NSIICoq4MDzPZc00DlGJTgHOCwTBSObSTeRTh3E0tQ,30429
torchvision/models/_utils.py,sha256=gOh6tA08U7NHLGt6zznG7dLJfi18fWy_Wp_dhaH8xko,11136
torchvision/models/alexnet.py,sha256=XcldP2UuOkdOUfdxitGS8qHzLH78Ny7VCzTzKsaWITU,4607
torchvision/models/convnext.py,sha256=xM6IWUmP3-TSn79_APA4WDSgzL5JSRXvIleJyVmmRYg,15762
torchvision/models/densenet.py,sha256=Q1_8tERSWn2uwhjylxkQlMVcYlirp3ARyxZEiG-Zw2Q,17260
torchvision/models/detection/__init__.py,sha256=D4cs338Z4BQn5TgX2IKuJC9TD2rtw2svUDZlALR-lwI,175
torchvision/models/detection/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-311.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-311.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-311.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-311.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-311.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-311.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-311.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-311.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-311.pyc,,
torchvision/models/detection/_utils.py,sha256=dee4UORMRBeHz4YKTOOeyKsEb9Q4AYmL3iG__sfZioM,22646
torchvision/models/detection/anchor_utils.py,sha256=M6Gm4Qrji8DSfR3IVpfS9VHNEpIKT_AEHJlINioIzns,12121
torchvision/models/detection/backbone_utils.py,sha256=SC0TRbPxy3P6Lzq0Z9iYRhHsoIGUex43v7ntyFJUwPU,10780
torchvision/models/detection/faster_rcnn.py,sha256=3ZiR2D3XovDfdIHBJjFfLHwxxfgIDvSfLh-pmMWvNNM,37812
torchvision/models/detection/fcos.py,sha256=0uOtXF6cOZhaSeTIB1rqreAC3XdcD4WXzk4QTMaF89A,34991
torchvision/models/detection/generalized_rcnn.py,sha256=7ovthQx8aEi6oxXovSqO7D7Qanp2yzd1HHXAQbQS3Tk,5062
torchvision/models/detection/image_list.py,sha256=rUmPJI-F1EQ_0nNsNnHrhsMc8fusrDFgUdloMLOJbgo,774
torchvision/models/detection/keypoint_rcnn.py,sha256=nXGqgQ6uhLLYw785Ta3fFKKL7Mc0KFp8QZG0sqY3T7k,22455
torchvision/models/detection/mask_rcnn.py,sha256=W_3sXiSIgFz5gE9fmSh-cw9NAt45CX-TGkLTqp-zvsg,27303
torchvision/models/detection/retinanet.py,sha256=nZNicKRgBZuJh-cM-FqPe9GgaB6GFOIgpopSmz4HE6M,38184
torchvision/models/detection/roi_heads.py,sha256=kzLNPjrU3jtWeXGqaFbdBPKZ4gAO5p77zlOJRiPkRg0,34699
torchvision/models/detection/rpn.py,sha256=AP9-p3_pujcDita2wSiRhbLnU8oSTFDPZNjw0w7ezUM,16205
torchvision/models/detection/ssd.py,sha256=BCU2bgRnPGmxsVFXriz54lsS7Xbdzdfm3N3BmW4OeI8,29642
torchvision/models/detection/ssdlite.py,sha256=_MKsJEExuIUtqrJ-BWcXCpyTK-jE4jjvaOgn4lUjn64,13538
torchvision/models/detection/transform.py,sha256=Jqtqf9uLo0uBEraJ1pDaMBSuALY5_vDELnLCQcOwwm0,12489
torchvision/models/efficientnet.py,sha256=_qqZVYMrNIhUw97v2B_9cgyLprm84hQk8A-X5m6uk4Q,44230
torchvision/models/feature_extraction.py,sha256=wu9xI_QcRW4XXQYggrveacZI74yAWbr7j-ckW8uHj-M,28521
torchvision/models/googlenet.py,sha256=aIlM44ZT8hsuRz3t2RLdcZv1vhYQP_3x8Rc-QGE27RU,13138
torchvision/models/inception.py,sha256=3DNzEmfmschGRrCyA4-mDkRVUBbv7GSnbZa_oaiaVYU,19316
torchvision/models/maxvit.py,sha256=LHPsHqa-RaWIqbbdMrsm2YEk1332RgJS6wIJ-agqPX4,32944
torchvision/models/mnasnet.py,sha256=PkF9p5stpT-y6O04OE5G_150yaFfEZqpZXlb0D1kWnw,17996
torchvision/models/mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision/models/mobilenetv2.py,sha256=G0ZvYkujutNjjPWJ1aJh-O0d3F02ExdvajeLyZHVr4Y,9964
torchvision/models/mobilenetv3.py,sha256=jIF7HMwQ7fqCPtWY1jw7PT17KMIIVYEXRAq17InrQSI,16724
torchvision/models/optical_flow/__init__.py,sha256=uuRFAdvcDobdAbY2VmxEZ7_CLH_f5-JRkCSuJRkj4RY,21
torchvision/models/optical_flow/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-311.pyc,,
torchvision/models/optical_flow/_utils.py,sha256=PRcuU-IB6EL3hAOLiyC5q-NBzlvIKfhSF_BMplHbzfY,2125
torchvision/models/optical_flow/raft.py,sha256=H96CpsOcUd5nyX8kFyOyTLWB6sI7txsWUMgqjH1jduM,40938
torchvision/models/quantization/__init__.py,sha256=YOJmYqWQTfP5P2ypteZNKQOMW4VEB2WHJlYoSlSaL1Y,130
torchvision/models/quantization/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-311.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-311.pyc,,
torchvision/models/quantization/googlenet.py,sha256=KfUQoqXi8JluXlJ66t2aUBjFNDN0B5Y6zsu29tWdnYQ,8324
torchvision/models/quantization/inception.py,sha256=9cb3yiBYnmaC2vC4LEFYbWPvO8iMFNcYQx6LksHmomc,11116
torchvision/models/quantization/mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision/models/quantization/mobilenetv2.py,sha256=xR7Vpq7xcq2GSdsxq6UT6OA5LEPaWFmxF2NnxRFnzVY,6071
torchvision/models/quantization/mobilenetv3.py,sha256=ZNb3egv-RdSjIW2nw6mEn6xE13AQZmDryMdDe1B96yg,9495
torchvision/models/quantization/resnet.py,sha256=0fzTtBt-1iP0CembaGBoRckI2akVSz2MxKzGfBx5xMQ,18547
torchvision/models/quantization/shufflenetv2.py,sha256=jnrMO3Js8rr3EZzAQi4AQvyHo8wl0zr2EqmRRjwU8o8,17441
torchvision/models/quantization/utils.py,sha256=mwO6t0K7PMcev2LLndIEmsXNKvYEJBM4f7NzsiM8jk4,2103
torchvision/models/regnet.py,sha256=vblO9fo8WnU6tbVnNZuHIabNrXyoVHnGVQDru2yRbEU,65105
torchvision/models/resnet.py,sha256=fDKRmPQU5BHdZNZ3hMH377sehb244WC4xdXFYZ-asRk,39905
torchvision/models/segmentation/__init__.py,sha256=TLL2SSmqE08HLiv_yyIWyIyrvf2xaOsZi0muDv_Y5Vc,69
torchvision/models/segmentation/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-311.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-311.pyc,,
torchvision/models/segmentation/_utils.py,sha256=P8658dSbojoau7DLTiE4RZmDZBX7AfgE2V7FjrP_YXs,1228
torchvision/models/segmentation/deeplabv3.py,sha256=krcN6ustrnFvTmdD2MEc7821lCSUjMWitedz6Zw0IbQ,15433
torchvision/models/segmentation/fcn.py,sha256=mQ1Wi4S9j5G6OQbNciuxNwVbJ6e9miTzIWj6mUF5JwA,9205
torchvision/models/segmentation/lraspp.py,sha256=QvcS-sGmSpJg3HQdi4e09jRLd8nPHIE05_GPbMvryRA,7815
torchvision/models/shufflenetv2.py,sha256=Z-0YBL0T3oLOcjX6CBCj755n3nUbDN0u8-q3xhsTWXY,15846
torchvision/models/squeezenet.py,sha256=Dha-ci350KU15D0LS9N07kw6MlNuusUHSBnC83Ery_E,8986
torchvision/models/swin_transformer.py,sha256=vjt095dNfuvMX1-WEG72tqe_xxAtsBLQytts88L2vB8,40364
torchvision/models/vgg.py,sha256=1SWU2kj-cjL7YDnV0dOh15Om9D_zSt3Ue4d0CjUBIt4,19724
torchvision/models/video/__init__.py,sha256=xHHR5c6kP0cMDXck7XnXq19iJL_Uemcxg3OC00cqE6A,97
torchvision/models/video/__pycache__/__init__.cpython-311.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-311.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-311.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-311.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-311.pyc,,
torchvision/models/video/mvit.py,sha256=_JaX-FjLbaW3YQGu6WdG6ZTsi0dZO3GVMpNkG-SYu8U,33904
torchvision/models/video/resnet.py,sha256=7XjOvzBVJQQmIXCTNcaSfrvKMK56BBg3wt2uKhjLsnk,17283
torchvision/models/video/s3d.py,sha256=Rn-iypP13jrETAap1Qd4NY6kkpYDuSXjGkEKZDOxemI,8034
torchvision/models/video/swin_transformer.py,sha256=vqE3_gXMbfgaWmrxRB1pp8fh6aqnpz-djmgKhtML3c4,28418
torchvision/models/vision_transformer.py,sha256=qDIjgxi0xAapBRyriUFTjuIsTeWiQQ02nZaks7ShCoE,32988
torchvision/ops/__init__.py,sha256=7wibGxcF1JHDviSNs9O9Pwlc8dhMSFfZo0wzVjTFnAY,2001
torchvision/ops/__pycache__/__init__.cpython-311.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-311.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-311.pyc,,
torchvision/ops/__pycache__/_utils.cpython-311.pyc,,
torchvision/ops/__pycache__/boxes.cpython-311.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-311.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-311.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-311.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-311.pyc,,
torchvision/ops/__pycache__/misc.cpython-311.pyc,,
torchvision/ops/__pycache__/poolers.cpython-311.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-311.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-311.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-311.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-311.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-311.pyc,,
torchvision/ops/_box_convert.py,sha256=_Uu8BkweU4hXeaUfUEiNZ1BC-O3FPo2Ra_cu3VPOjG4,7188
torchvision/ops/_register_onnx_ops.py,sha256=g4M5Fp7n_5ZTzIQcUXvEct3YFlUMPNVSQQfBP-J0eQQ,4288
torchvision/ops/_utils.py,sha256=MxfEBDZ9tnL-QIYyDj6FEmbE6MPQZtfOljz2Yhy-Oxg,3723
torchvision/ops/boxes.py,sha256=7AIAXk-UxMX94nk7DBbUCX6bkQ_brB1jm7K4LWBMxFI,18780
torchvision/ops/ciou_loss.py,sha256=EKl6TTnEFJpUlFgS3iaPuslllzxLm19G1h8B2duVx0s,2832
torchvision/ops/deform_conv.py,sha256=NyILJV9kq_nui7-rMjSCeHGATM_zZzags-6bNfBpdtc,7178
torchvision/ops/diou_loss.py,sha256=m8ML9PsaYosJHPe-8KBnj69ZoBmAcznf3V3WaEP7IfQ,3426
torchvision/ops/drop_block.py,sha256=ZkIzM1b3v5_U7z0eavzaNpN7IBq0N4ZNwwvWArispwg,6010
torchvision/ops/feature_pyramid_network.py,sha256=Ojq68D4xf7I1ii6HQ1UYX6PDS5zzTuJZBNxbVUva3Uw,8933
torchvision/ops/focal_loss.py,sha256=A-Ec5GG7sbyE8ydGP6QuAPdtkUbDfdg5j4zYvs5PwzA,2480
torchvision/ops/giou_loss.py,sha256=SQ42KOFbx9pAJ-n1r9MwT3Hu5M890chnUL6oP0pLzRs,2770
torchvision/ops/misc.py,sha256=VjA5TrbwRXXZxLc0ATAZnMNH8oXZj0Gn0XO8u_2X8E0,13907
torchvision/ops/poolers.py,sha256=WPb3VxCC6fAhCCmDarUVasTkRbUQnteqZnaT9fESb9k,12228
torchvision/ops/ps_roi_align.py,sha256=6_kmnE6z_3AZZ1N2hrS_uK3cbuzqZhjdM2rC50mfYUo,3715
torchvision/ops/ps_roi_pool.py,sha256=2JrjJwzVtEeEg0BebkCnGfq4xEDwMCD-Xh915mvNcyI,2940
torchvision/ops/roi_align.py,sha256=eL--jezfuGpIjNh9FZNrL-1tjGWXHZCJ2tlN-63Vvkk,11608
torchvision/ops/roi_pool.py,sha256=kbvY49SbmfuSeKaObttS5Tbz8PztGubNpzRfHsBATM8,3009
torchvision/ops/stochastic_depth.py,sha256=9T4Zu_BaemKZafSmRwrPCVr5aaGH8tmzlsQAZO-1_-Y,2302
torchvision/transforms/__init__.py,sha256=WCNXTJUbJ1h7YaN9UfrBSvt--ST2PAV4sLICbTS-L5A,55
torchvision/transforms/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-311.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-311.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-311.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-311.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-311.pyc,,
torchvision/transforms/__pycache__/functional.cpython-311.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-311.pyc,,
torchvision/transforms/_functional_pil.py,sha256=QCm2U14OXmqGsZ87B8U86yxeXMmxm3LqRzk3-_WOwyc,12514
torchvision/transforms/_functional_tensor.py,sha256=tZ2tPIT8lrnUwcFtw_6rL9ZLCbY1Lk2BXPiGEuNFTxs,34888
torchvision/transforms/_functional_video.py,sha256=c4BbUi3Y2LvskozFdy619piLBd5acsjxgogYAXmY5P8,3971
torchvision/transforms/_presets.py,sha256=iX5Lr8qZvHA-UxOAjBH-iTiu5m6l2hQRwJROsxYDs_w,8721
torchvision/transforms/_transforms_video.py,sha256=ub2gCT5ELiK918Bq-Pp6mzhWrAZxlj7blfpkA8Dhb1o,5124
torchvision/transforms/autoaugment.py,sha256=WwWSYH-q71z748AVQu9Mtf6Fo6xcmmbwsMCgS22-67M,28839
torchvision/transforms/functional.py,sha256=LKl3poyvirCF5LmBZJg2aHd-5_NgHbXl5FHEg6br-ak,69447
torchvision/transforms/transforms.py,sha256=unNbrmoirvPbDuMj4m7VVwrPknwmPK7bmp6Vv5Innkw,87710
torchvision/transforms/v2/__init__.py,sha256=Jiz1DjLV-_zsbKfLGYL-aSy0jJ0chEiBxa4X8xdh1uk,1638
torchvision/transforms/v2/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-311.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-311.pyc,,
torchvision/transforms/v2/_augment.py,sha256=tnnle6FAHq4-xdemNBygk0PIfJDBaNOC9rUM4ir1jJo,16725
torchvision/transforms/v2/_auto_augment.py,sha256=EsuyOv_ZwIALwsNMNwJOaFg5TOl-5_oNlELbYNxObho,32868
torchvision/transforms/v2/_color.py,sha256=IcieYLHLRa4FGXEeC1fGFunMjJ0pjpmsoKo_ulVDhFo,17378
torchvision/transforms/v2/_container.py,sha256=w64QbUa9c1dz1z5DCZD8v9p3PIE75_TCnxOq11ltksc,6245
torchvision/transforms/v2/_deprecated.py,sha256=EA9nX2T5it6sJiOup2BEtaEynDh6lzzSWZzTH5fcrPw,1990
torchvision/transforms/v2/_geometry.py,sha256=egxkjRR8myPJqJr_y-TKOIokmfA2oRfzulWaDAWJcFE,69141
torchvision/transforms/v2/_meta.py,sha256=J9s54OCuhC2aOlSfZAA9qO-xaXnjOCJtWk952ORBxYw,3246
torchvision/transforms/v2/_misc.py,sha256=OjlArnmKva9zlemi29pBrctuIiIl-y4QDoveuPrqlYA,19563
torchvision/transforms/v2/_temporal.py,sha256=LnmhVdwGsRYK4jk1H_oSOSlQO5_PdbRrRPFFLDx1pO8,925
torchvision/transforms/v2/_transform.py,sha256=sjB32n5ZOn1KpyeQPJBZZZGEg4hn8AO935FHctqmqnU,9510
torchvision/transforms/v2/_type_conversion.py,sha256=GOL0jtQfaxp4S2cw9ZfudSr9ZELxWIdvbcjJnlpuyec,2934
torchvision/transforms/v2/_utils.py,sha256=p9e_I0mSXPTaBz935lx6KFUsrWetj55K9ZDs17WmRAQ,9075
torchvision/transforms/v2/functional/__init__.py,sha256=NFWFO9kqlOnlBxVCjD1LNmarFdYmcjyoNOjesi3ABxk,4027
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-311.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-311.pyc,,
torchvision/transforms/v2/functional/_augment.py,sha256=oFPymp04zhQpTQR3O-5XUJHDnQW6-pfwWJKoP-dpWkM,3579
torchvision/transforms/v2/functional/_color.py,sha256=hLx_h4TjXFz0dZsarb3nIAFWH6h3r3Wn1lY3rlgvh8o,31117
torchvision/transforms/v2/functional/_deprecated.py,sha256=-Aza1I4OSqpbF7skVMIJA7YA6jYDgK7A54MTsSn95oI,819
torchvision/transforms/v2/functional/_geometry.py,sha256=BiYEQ3x2o94GssFEx56713_0AQHjfjosYmdxw44UewE,115534
torchvision/transforms/v2/functional/_meta.py,sha256=cQb2iEk3rCcrgc7OeDz84_1RPYQQisTEte4gYEMDw-M,29602
torchvision/transforms/v2/functional/_misc.py,sha256=Gcatc6Qk3Wy1E_aa0EqucY-OKflap8hZWA3Bv29Bu40,18744
torchvision/transforms/v2/functional/_temporal.py,sha256=tSRkkqOqUQ0QXjENF82F16El1-J0IDoFKIH-ss_cpC4,1163
torchvision/transforms/v2/functional/_type_conversion.py,sha256=oYf4LMgiClvEZwwc3WbKI7fJ-rRFhDrVSBKiPA5vxio,896
torchvision/transforms/v2/functional/_utils.py,sha256=8yfZCpdXKX5Py-qZgYEq8Kd2Vp98iZ3zyWLPGmsym2k,5630
torchvision/tv_tensors/__init__.py,sha256=1kRzZyGHtOwFK0dAhJqGtSjEa5buDg_L6ygwg8EnXWQ,1837
torchvision/tv_tensors/__pycache__/__init__.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_keypoints.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-311.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-311.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,sha256=E36IiYCDJcFGiFWaWGbhDWM3j0ypuB8Yq6gdVwqDYRs,7902
torchvision/tv_tensors/_dataset_wrapper.py,sha256=pgcasnVwGiQb0mmEqPkIgycI-4AV703xbBRwUFbMo9k,25171
torchvision/tv_tensors/_image.py,sha256=yg9LaAPSwpKWx08bJtTvwhuLwYfWdGk-A0AY8kpX9hw,2016
torchvision/tv_tensors/_keypoints.py,sha256=A-CzXaDhwsswZh0zQyn9Lkk_BjSllvZLTcOK659mGOM,4686
torchvision/tv_tensors/_mask.py,sha256=H7wiK_uB9oiVQu22H1PWghyipLMmndb9pnoGIPLzqG4,1486
torchvision/tv_tensors/_torch_function_helpers.py,sha256=vr1G4egyQfRjUtDedTWRope1gP4OB1hzAjKFZGXfc2Y,2402
torchvision/tv_tensors/_tv_tensor.py,sha256=RhkzUeyCELJvcm_n4WW0HCDKNCx7zRKQVBocc-Esfhw,6354
torchvision/tv_tensors/_video.py,sha256=r5_pwyvMM5h10swXEb1NhrKXAmI2GWY4ijNhD6gn24Y,1422
torchvision/utils.py,sha256=-nt13yHCLEWlKSZi7-0y9XJUUhV-8f-aAbwCbNDGw3A,34597
torchvision/version.py,sha256=NOPcDyq_L8tXKwjl_N5jpg-wRaX9PeECt9lve7mROyw,206
torchvision/zlib.dll,sha256=jb_W73N0qDEVi93Mt549VmXpYlyBr1V_FbQVC3h39oc,99608
