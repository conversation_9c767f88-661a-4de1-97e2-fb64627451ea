#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye多模态大语言模型自动部署脚本
作者：Python开发猫娘 🐾
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class KeyeDeployer:
    def __init__(self):
        self.base_dir = Path("E:/python/keye")
        self.cache_dir = self.base_dir / "cache"
        self.models_dir = self.base_dir / "models"
        self.examples_dir = self.base_dir / "examples"
        self.logs_dir = self.base_dir / "logs"
        
    def print_step(self, step, message):
        """打印步骤信息"""
        print(f"\n🐾 步骤 {step}: {message}")
        print("=" * 50)
    
    def run_command(self, command, check=True):
        """执行命令"""
        print(f"执行命令: {command}")
        try:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True, encoding='utf-8')
            if result.stdout:
                print(result.stdout)
            return result.returncode == 0
        except subprocess.CalledProcessError as e:
            print(f"命令执行失败: {e}")
            if e.stderr:
                print(f"错误信息: {e.stderr}")
            return False
    
    def create_directories(self):
        """创建项目目录结构"""
        self.print_step(1, "创建项目目录结构")
        
        directories = [self.base_dir, self.cache_dir, self.models_dir, 
                      self.examples_dir, self.logs_dir]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
    
    def setup_environment(self):
        """设置环境变量"""
        self.print_step(2, "配置环境变量")
        
        env_vars = {
            'HF_HOME': str(self.cache_dir),
            'TRANSFORMERS_CACHE': str(self.cache_dir),
            'HF_HUB_CACHE': str(self.cache_dir),
            'VIDEO_MAX_PIXELS': '896000'
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            print(f"✅ 设置环境变量: {key} = {value}")
    
    def check_gpu(self):
        """检查GPU状态"""
        self.print_step(3, "检查GPU状态")
        
        try:
            import torch
            print(f"PyTorch版本: {torch.__version__}")
            print(f"CUDA可用: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                print(f"GPU数量: {torch.cuda.device_count()}")
                print(f"GPU名称: {torch.cuda.get_device_name(0)}")
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                print(f"GPU内存: {gpu_memory:.1f} GB")
                
                if gpu_memory < 12:
                    print("⚠️  警告: GPU内存可能不足，建议使用CPU推理或量化模型")
            else:
                print("⚠️  警告: 未检测到CUDA GPU，将使用CPU推理")
        except ImportError:
            print("❌ PyTorch未安装，将在后续步骤中安装")
    
    def install_dependencies(self):
        """安装依赖包"""
        self.print_step(4, "安装依赖包")
        
        # 基础依赖
        basic_deps = [
            "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
            "transformers>=4.37.0",
            "accelerate",
            "pillow",
            "requests",
            "packaging",
            "av",
            "keye-vl-utils"
        ]
        
        for dep in basic_deps:
            print(f"\n安装: {dep}")
            if not self.run_command(f"pip install {dep}"):
                print(f"❌ 安装失败: {dep}")
                return False
        
        print("✅ 基础依赖安装完成")
        return True
    
    def clone_repository(self):
        """克隆项目仓库"""
        self.print_step(5, "克隆项目仓库")
        
        repo_dir = self.base_dir / "Keye"
        if repo_dir.exists():
            print("项目仓库已存在，跳过克隆")
            return True
        
        os.chdir(self.base_dir)
        return self.run_command("git clone https://github.com/Kwai-Keye/Keye.git")
    
    def create_test_scripts(self):
        """创建测试脚本"""
        self.print_step(6, "创建测试脚本")
        
        # 基础测试脚本
        basic_test = '''
import torch
import transformers
from keye_vl_utils import process_vision_info

print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
print(f"Transformers版本: {transformers.__version__}")
print("✅ keye-vl-utils导入成功!")
'''
        
        # 模型下载脚本
        download_script = '''
from transformers import AutoModel, AutoProcessor
import os

# 设置缓存目录
os.environ['HF_HOME'] = 'E:\\\\python\\\\keye\\\\cache'
os.environ['TRANSFORMERS_CACHE'] = 'E:\\\\python\\\\keye\\\\cache'

model_path = "Kwai-Keye/Keye-VL-8B-Preview"

print("开始下载模型...")
try:
    processor = AutoProcessor.from_pretrained(
        model_path, 
        trust_remote_code=True,
        cache_dir="E:\\\\python\\\\keye\\\\cache"
    )
    print("✅ 处理器下载完成!")
    
    model = AutoModel.from_pretrained(
        model_path,
        torch_dtype="auto",
        trust_remote_code=True,
        cache_dir="E:\\\\python\\\\keye\\\\cache"
    )
    print("✅ 模型下载完成!")
    
except Exception as e:
    print(f"❌ 下载出错: {e}")
'''
        
        # 图像测试脚本
        image_test = '''
from transformers import AutoModel, AutoProcessor
from keye_vl_utils import process_vision_info
import torch

print("加载模型...")
model_path = "Kwai-Keye/Keye-VL-8B-Preview"

try:
    model = AutoModel.from_pretrained(
        model_path,
        torch_dtype="auto",
        device_map="auto",
        trust_remote_code=True,
    )
    processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
    print("✅ 模型加载成功!")
    
    # 测试图像理解
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "请描述这张图片。"},
            ],
        }
    ]
    
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
        **mm_processor_kwargs
    )
    
    if torch.cuda.is_available():
        inputs = inputs.to("cuda")
    
    print("生成回复中...")
    generated_ids = model.generate(**inputs, max_new_tokens=512)
    generated_ids_trimmed = [
        out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_text = processor.batch_decode(
        generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
    )
    print("🎉 图像描述:", output_text[0])
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
'''
        
        # 保存测试脚本
        scripts = {
            "test_installation.py": basic_test,
            "download_model.py": download_script,
            "test_image_understanding.py": image_test
        }
        
        for filename, content in scripts.items():
            script_path = self.examples_dir / filename
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 创建测试脚本: {filename}")
    
    def run_installation_test(self):
        """运行安装测试"""
        self.print_step(7, "运行安装测试")
        
        test_script = self.examples_dir / "test_installation.py"
        os.chdir(self.examples_dir)
        return self.run_command(f"python {test_script}")
    
    def deploy(self):
        """执行完整部署流程"""
        print("🐾 开始部署Keye多模态大语言模型")
        print("=" * 60)
        
        steps = [
            self.create_directories,
            self.setup_environment,
            self.check_gpu,
            self.install_dependencies,
            self.clone_repository,
            self.create_test_scripts,
            self.run_installation_test
        ]
        
        for i, step in enumerate(steps, 1):
            try:
                if not step():
                    print(f"❌ 步骤 {i} 失败，部署中断")
                    return False
            except Exception as e:
                print(f"❌ 步骤 {i} 出现异常: {e}")
                return False
        
        print("\n🎉 基础部署完成！")
        print("\n📝 下一步操作：")
        print("1. 运行模型下载脚本：")
        print(f"   cd {self.examples_dir}")
        print("   python download_model.py")
        print("\n2. 测试图像理解功能：")
        print("   python test_image_understanding.py")
        print("\n3. 查看完整部署指南：")
        print("   E:\\python\\过程文件夹\\keye_deployment_guide.md")
        
        return True

if __name__ == "__main__":
    deployer = KeyeDeployer()
    success = deployer.deploy()
    
    if success:
        print("\n✅ 部署脚本执行完成！")
    else:
        print("\n❌ 部署过程中遇到问题，请查看错误信息")
    
    input("\n按回车键退出...")
