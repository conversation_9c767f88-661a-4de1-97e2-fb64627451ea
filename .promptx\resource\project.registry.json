{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-09-08T03:53:43.761Z", "updatedAt": "2025-09-08T03:53:43.797Z", "resourceCount": 34}, "resources": [{"id": "workspace-structure", "source": "project", "protocol": "knowledge", "name": "Workspace Structure 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/knowledge/workspace-structure.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.765Z", "updatedAt": "2025-09-08T03:53:43.765Z", "scannedAt": "2025-09-08T03:53:43.765Z", "path": "knowledge/workspace-structure.knowledge.md"}}, {"id": "da<PERSON><PERSON>", "source": "project", "protocol": "role", "name": "Daxiong 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/daxiong/daxiong.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.766Z", "updatedAt": "2025-09-08T03:53:43.766Z", "scannedAt": "2025-09-08T03:53:43.766Z", "path": "role/daxiong/daxiong.role.md"}}, {"id": "anti-ai-detection", "source": "project", "protocol": "execution", "name": "Anti Ai Detection 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/anti-ai-detection.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.768Z", "updatedAt": "2025-09-08T03:53:43.768Z", "scannedAt": "2025-09-08T03:53:43.768Z", "path": "role/daxiong/execution/anti-ai-detection.execution.md"}}, {"id": "daxiong-workflow", "source": "project", "protocol": "execution", "name": "Daxiong Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/daxiong-workflow.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.769Z", "updatedAt": "2025-09-08T03:53:43.769Z", "scannedAt": "2025-09-08T03:53:43.769Z", "path": "role/daxiong/execution/daxiong-workflow.execution.md"}}, {"id": "enhanced-capabilities", "source": "project", "protocol": "execution", "name": "Enhanced Capabilities 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/daxiong/execution/enhanced-capabilities.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.769Z", "updatedAt": "2025-09-08T03:53:43.769Z", "scannedAt": "2025-09-08T03:53:43.769Z", "path": "role/daxiong/execution/enhanced-capabilities.execution.md"}}, {"id": "workspace-management", "source": "project", "protocol": "execution", "name": "Workspace Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/workspace-management.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.792Z", "updatedAt": "2025-09-08T03:53:43.792Z", "scannedAt": "2025-09-08T03:53:43.792Z", "path": "role/xiongbao/execution/workspace-management.execution.md"}}, {"id": "a-stock-expertise", "source": "project", "protocol": "knowledge", "name": "A Stock Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/a-stock-expertise.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.771Z", "updatedAt": "2025-09-08T03:53:43.771Z", "scannedAt": "2025-09-08T03:53:43.771Z", "path": "role/daxiong/knowledge/a-stock-expertise.knowledge.md"}}, {"id": "anti-ai-detection-experience", "source": "project", "protocol": "knowledge", "name": "Anti Ai Detection Experience 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/anti-ai-detection-experience.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.772Z", "updatedAt": "2025-09-08T03:53:43.772Z", "scannedAt": "2025-09-08T03:53:43.772Z", "path": "role/daxiong/knowledge/anti-ai-detection-experience.knowledge.md"}}, {"id": "content-creation", "source": "project", "protocol": "knowledge", "name": "Content Creation 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/daxiong/knowledge/content-creation.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.773Z", "updatedAt": "2025-09-08T03:53:43.773Z", "scannedAt": "2025-09-08T03:53:43.773Z", "path": "role/daxiong/knowledge/content-creation.knowledge.md"}}, {"id": "daxiong-mindset", "source": "project", "protocol": "thought", "name": "Daxiong Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/daxiong/thought/daxiong-mindset.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.774Z", "updatedAt": "2025-09-08T03:53:43.774Z", "scannedAt": "2025-09-08T03:53:43.774Z", "path": "role/daxiong/thought/daxiong-mindset.thought.md"}}, {"id": "interview-question-generation", "source": "project", "protocol": "execution", "name": "Interview Question Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/interviewer-assistant/execution/interview-question-generation.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.775Z", "updatedAt": "2025-09-08T03:53:43.775Z", "scannedAt": "2025-09-08T03:53:43.775Z", "path": "role/interviewer-assistant/execution/interview-question-generation.execution.md"}}, {"id": "interviewer-assistant", "source": "project", "protocol": "role", "name": "Interviewer Assistant 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/interviewer-assistant/interviewer-assistant.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.776Z", "updatedAt": "2025-09-08T03:53:43.776Z", "scannedAt": "2025-09-08T03:53:43.776Z", "path": "role/interviewer-assistant/interviewer-assistant.role.md"}}, {"id": "interview-analysis", "source": "project", "protocol": "thought", "name": "Interview Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/interviewer-assistant/thought/interview-analysis.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.777Z", "updatedAt": "2025-09-08T03:53:43.777Z", "scannedAt": "2025-09-08T03:53:43.777Z", "path": "role/interviewer-assistant/thought/interview-analysis.thought.md"}}, {"id": "ppt-pipeline", "source": "project", "protocol": "execution", "name": "Ppt Pipeline 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ppt-assistant/execution/ppt-pipeline.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.778Z", "updatedAt": "2025-09-08T03:53:43.778Z", "scannedAt": "2025-09-08T03:53:43.778Z", "path": "role/ppt-assistant/execution/ppt-pipeline.execution.md"}}, {"id": "ppt-output-rules", "source": "project", "protocol": "knowledge", "name": "Ppt Output Rules 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/ppt-assistant/knowledge/ppt-output-rules.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.779Z", "updatedAt": "2025-09-08T03:53:43.779Z", "scannedAt": "2025-09-08T03:53:43.779Z", "path": "role/ppt-assistant/knowledge/ppt-output-rules.knowledge.md"}}, {"id": "ppt-assistant", "source": "project", "protocol": "role", "name": "Ppt Assistant 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ppt-assistant/ppt-assistant.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.780Z", "updatedAt": "2025-09-08T03:53:43.780Z", "scannedAt": "2025-09-08T03:53:43.780Z", "path": "role/ppt-assistant/ppt-assistant.role.md"}}, {"id": "ppt-thinking", "source": "project", "protocol": "thought", "name": "Ppt Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ppt-assistant/thought/ppt-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.780Z", "updatedAt": "2025-09-08T03:53:43.780Z", "scannedAt": "2025-09-08T03:53:43.780Z", "path": "role/ppt-assistant/thought/ppt-thinking.thought.md"}}, {"id": "python-cat-workflow", "source": "project", "protocol": "execution", "name": "Python Cat Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-cat-workflow.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.781Z", "updatedAt": "2025-09-08T03:53:43.781Z", "scannedAt": "2025-09-08T03:53:43.781Z", "path": "role/python-cat-girl/execution/python-cat-workflow.execution.md"}}, {"id": "python-standards", "source": "project", "protocol": "execution", "name": "Python Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-cat-girl/execution/python-standards.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.782Z", "updatedAt": "2025-09-08T03:53:43.782Z", "scannedAt": "2025-09-08T03:53:43.782Z", "path": "role/python-cat-girl/execution/python-standards.execution.md"}}, {"id": "python-expertise", "source": "project", "protocol": "knowledge", "name": "Python Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-cat-girl/knowledge/python-expertise.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.783Z", "updatedAt": "2025-09-08T03:53:43.783Z", "scannedAt": "2025-09-08T03:53:43.783Z", "path": "role/python-cat-girl/knowledge/python-expertise.knowledge.md"}}, {"id": "python-cat-girl", "source": "project", "protocol": "role", "name": "Python Cat Girl 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-cat-girl/python-cat-girl.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.784Z", "updatedAt": "2025-09-08T03:53:43.784Z", "scannedAt": "2025-09-08T03:53:43.784Z", "path": "role/python-cat-girl/python-cat-girl.role.md"}}, {"id": "python-cat-thinking", "source": "project", "protocol": "thought", "name": "Python Cat Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-cat-girl/thought/python-cat-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.785Z", "updatedAt": "2025-09-08T03:53:43.785Z", "scannedAt": "2025-09-08T03:53:43.785Z", "path": "role/python-cat-girl/thought/python-cat-thinking.thought.md"}}, {"id": "html-standards", "source": "project", "protocol": "execution", "name": "Html Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/html-standards.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.786Z", "updatedAt": "2025-09-08T03:53:43.786Z", "scannedAt": "2025-09-08T03:53:43.786Z", "path": "role/web-expert/execution/html-standards.execution.md"}}, {"id": "web-design-workflow", "source": "project", "protocol": "execution", "name": "Web Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-expert/execution/web-design-workflow.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.787Z", "updatedAt": "2025-09-08T03:53:43.787Z", "scannedAt": "2025-09-08T03:53:43.787Z", "path": "role/web-expert/execution/web-design-workflow.execution.md"}}, {"id": "web-expertise", "source": "project", "protocol": "knowledge", "name": "Web Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/web-expert/knowledge/web-expertise.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.788Z", "updatedAt": "2025-09-08T03:53:43.788Z", "scannedAt": "2025-09-08T03:53:43.788Z", "path": "role/web-expert/knowledge/web-expertise.knowledge.md"}}, {"id": "web-design-thinking", "source": "project", "protocol": "thought", "name": "Web Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-expert/thought/web-design-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.789Z", "updatedAt": "2025-09-08T03:53:43.789Z", "scannedAt": "2025-09-08T03:53:43.789Z", "path": "role/web-expert/thought/web-design-thinking.thought.md"}}, {"id": "web-expert", "source": "project", "protocol": "role", "name": "Web Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-expert/web-expert.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.789Z", "updatedAt": "2025-09-08T03:53:43.789Z", "scannedAt": "2025-09-08T03:53:43.789Z", "path": "role/web-expert/web-expert.role.md"}}, {"id": "autonovels-core-prompts", "source": "project", "protocol": "execution", "name": "Autonovels Core Prompts 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/autonovels-core-prompts.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.791Z", "updatedAt": "2025-09-08T03:53:43.791Z", "scannedAt": "2025-09-08T03:53:43.791Z", "path": "role/xiongbao/execution/autonovels-core-prompts.execution.md"}}, {"id": "novel-writing-workflow", "source": "project", "protocol": "execution", "name": "Novel Writing Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/xiongbao/execution/novel-writing-workflow.execution.md", "metadata": {"createdAt": "2025-09-08T03:53:43.792Z", "updatedAt": "2025-09-08T03:53:43.792Z", "scannedAt": "2025-09-08T03:53:43.792Z", "path": "role/xiongbao/execution/novel-writing-workflow.execution.md"}}, {"id": "autonovels-system", "source": "project", "protocol": "knowledge", "name": "Autonovels System 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/autonovels-system.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.793Z", "updatedAt": "2025-09-08T03:53:43.793Z", "scannedAt": "2025-09-08T03:53:43.793Z", "path": "role/xiongbao/knowledge/autonovels-system.knowledge.md"}}, {"id": "xiongbao-workspace", "source": "project", "protocol": "knowledge", "name": "Xiongbao Workspace 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/xiongbao/knowledge/xiongbao-workspace.knowledge.md", "metadata": {"createdAt": "2025-09-08T03:53:43.794Z", "updatedAt": "2025-09-08T03:53:43.794Z", "scannedAt": "2025-09-08T03:53:43.794Z", "path": "role/xiongbao/knowledge/xiongbao-workspace.knowledge.md"}}, {"id": "autonovels-creation-thinking", "source": "project", "protocol": "thought", "name": "Autonovels Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/autonovels-creation-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.795Z", "updatedAt": "2025-09-08T03:53:43.795Z", "scannedAt": "2025-09-08T03:53:43.795Z", "path": "role/xiongbao/thought/autonovels-creation-thinking.thought.md"}}, {"id": "novel-creation-thinking", "source": "project", "protocol": "thought", "name": "Novel Creation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/xiongbao/thought/novel-creation-thinking.thought.md", "metadata": {"createdAt": "2025-09-08T03:53:43.796Z", "updatedAt": "2025-09-08T03:53:43.796Z", "scannedAt": "2025-09-08T03:53:43.796Z", "path": "role/xiongbao/thought/novel-creation-thinking.thought.md"}}, {"id": "xiongbao", "source": "project", "protocol": "role", "name": "Xiongbao 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/xiongbao/xiongbao.role.md", "metadata": {"createdAt": "2025-09-08T03:53:43.796Z", "updatedAt": "2025-09-08T03:53:43.796Z", "scannedAt": "2025-09-08T03:53:43.796Z", "path": "role/xiongbao/xiongbao.role.md"}}], "stats": {"totalResources": 34, "byProtocol": {"knowledge": 9, "role": 6, "execution": 12, "thought": 7}, "bySource": {"project": 34}}}