import smtplib
import schedule
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import calendar


class EmailScheduler:
    def __init__(self, smtp_server, smtp_port, username, password, use_ssl=True):
        """初始化邮件发送器

        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            username: 邮箱账号
            password: 邮箱密码
            use_ssl: 是否使用SSL连接
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_ssl = use_ssl
        self.jobs = {}

    def send_email(self, to_email, subject, message, html=False, individual_emails=True):
        """发送邮件

        Args:
            to_email: 收件人邮箱，可以是单个字符串或邮箱列表
            subject: 邮件主题
            message: 邮件内容
            html: 是否为HTML格式
            individual_emails: 当有多个收件人时，是否分别发送单独的邮件给每个人

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        try:
            # 处理收件人
            if isinstance(to_email, str):
                recipients = [to_email]
            else:
                recipients = to_email

            # 连接SMTP服务器
            if self.use_ssl:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()  # 启用TLS加密

            server.login(self.username, self.password)

            # 如果是多个收件人且需要分别发送
            if len(recipients) > 1 and individual_emails:
                for recipient in recipients:
                    # 为每个收件人创建单独的邮件
                    msg = MIMEMultipart()
                    msg['From'] = self.username
                    msg['To'] = recipient
                    msg['Subject'] = subject

                    # 添加邮件内容
                    if html:
                        msg.attach(MIMEText(message, 'html', 'utf-8'))
                    else:
                        msg.attach(MIMEText(message, 'plain', 'utf-8'))

                    # 发送邮件
                    server.sendmail(self.username, [recipient], msg.as_string())
                    print(f"邮件已发送至 {recipient}，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                # 创建一封邮件发送给所有人（传统方式）
                msg = MIMEMultipart()
                msg['From'] = self.username
                msg['To'] = ", ".join(recipients)
                msg['Subject'] = subject

                # 添加邮件内容
                if html:
                    msg.attach(MIMEText(message, 'html', 'utf-8'))
                else:
                    msg.attach(MIMEText(message, 'plain', 'utf-8'))

                # 发送邮件
                server.sendmail(self.username, recipients, msg.as_string())
                if len(recipients) == 1:
                    print(f"邮件已发送至 {recipients[0]}，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                else:
                    print(
                        f"邮件已发送至 {len(recipients)} 位收件人，时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            server.quit()
            return True
        except Exception as e:
            print(f"发送邮件失败: {str(e)}")
            return False

    def send_immediate(self, to_email, subject, message, html=False, individual_emails=True):
        """立即发送邮件

        Args:
            to_email: 收件人邮箱，可以是单个字符串或邮箱列表
            subject: 邮件主题
            message: 邮件内容
            html: 是否为HTML格式
            individual_emails: 当有多个收件人时，是否分别发送单独的邮件给每个人

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        print("正在立即发送邮件...")
        return self.send_email(to_email, subject, message, html, individual_emails)

    def schedule_monthly(self, day, time_str, to_email, subject, message, html=False, individual_emails=True,
                         job_id=None):
        """每月指定日期定时发送邮件

        Args:
            day: 每月的第几天，1-31之间的整数
            time_str: 发送时间，格式为"HH:MM"，如"08:30"
            to_email: 收件人邮箱，可以是单个字符串或邮箱列表
            subject: 邮件主题
            message: 邮件内容
            html: 是否为HTML格式
            individual_emails: 当有多个收件人时，是否分别发送单独的邮件给每个人
            job_id: 任务ID，如果不提供则自动生成

        Returns:
            str: 任务ID
        """
        if not 1 <= day <= 31:
            raise ValueError("日期必须在1-31之间")

        if job_id is None:
            job_id = f"monthly_day{day}_{time_str}_{to_email if isinstance(to_email, str) else 'multiple'}"

        # 检查日期是否在当月有效（处理2月29日等情况）
        def check_and_send():
            now = datetime.now()
            # 获取当月最后一天
            last_day = calendar.monthrange(now.year, now.month)[1]

            # 如果指定的日期超过当月最后一天，则不发送
            if day > last_day:
                print(f"当月没有{day}日，跳过本次发送")
                return

            # 检查今天是否是指定的日期
            if now.day == day:
                self.send_email(to_email=to_email, subject=subject, message=message, html=html,
                                individual_emails=individual_emails)

        # 每天检查一次，如果是指定的日期则发送
        job = schedule.every().day.at(time_str).do(check_and_send)

        self.jobs[job_id] = job
        print(f"已创建每月定时任务: {job_id}，发送时间: 每月{day}日 {time_str}")
        return job_id

    def cancel_job(self, job_id):
        """取消定时任务

        Args:
            job_id: 任务ID

        Returns:
            bool: 取消成功返回True，失败返回False
        """
        if job_id in self.jobs:
            schedule.cancel_job(self.jobs[job_id])
            del self.jobs[job_id]
            print(f"已取消定时任务: {job_id}")
            return True
        else:
            print(f"未找到定时任务: {job_id}")
            return False

    def list_jobs(self):
        """列出所有定时任务

        Returns:
            list: 所有任务ID的列表
        """
        if not self.jobs:
            print("当前没有定时任务")
            return []

        job_list = []
        for job_id in self.jobs:
            job_list.append(job_id)
            print(f"定时任务: {job_id}")

        return job_list

    def run(self):
        """运行定时任务调度器"""
        print("定时邮件发送器已启动，按Ctrl+C停止...")
        try:
            while True:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            print("定时邮件发送器已停止")


# 使用示例
if __name__ == "__main__":
    sender_email = "<EMAIL>"  # 您的邮箱地址
    sender_password = "fkhd@508"  # 邮箱密码或授权码
    smtp_server = "smtp.faidns.com"  # SMTP服务器地址
    smtp_port = 465  # SMTP服务器端口
    use_ssl = True  # 是否使用SSL连接

    # 创建邮件调度器
    scheduler = EmailScheduler(
        smtp_server=smtp_server,
        smtp_port=smtp_port,
        username=sender_email,
        password=sender_password,
        use_ssl=use_ssl
    )

    date1 = 'Sep 10, 2025'
    message1 = f'''
    <div style="width:100%; position:relative; zoom:1; text-align:left;">
    <table cellspacing="0" cellpadding="0" style="border-spacing:0; width:100%;">
        <tr valign="top">
            <td>
                <div style="padding:10px;">
                    <div class="mailBody"><div style="font:14px/1.5 'Lucida Grande'; color:#333;"><p>Dear Na-young,</p><p>Thank you for choosing FAISCO PRO as your marketing tool! We’d like to remind you that your subscription will automatically renew in <strong>3 days</strong>.</p><ul><li><strong>Plan</strong>: FAISCO PRO</li><li><strong>Price</strong>: $69/month</li><li><strong>Renewal Date</strong>: {date1}</li></ul><p>If you’d like to continue enjoying the powerful features of the PRO plan, no action is required. The renewal will be processed using the payment method you previously provided.</p><p>Should you need to update your payment information or make adjustments to your subscription, please reply to this email, and we’ll be happy to assist you.</p><p>Thank you for your support of FAISCO! We look forward to continuing to provide you with efficient marketing solutions.</p><p>Best regards,<br>The FAISCO Team</p><div><div style="font: 14px/1.5 'Lucida Grande';"><div style="text-align: center;"><br></div><div style="text-align: center;"><br></div><div style="text-align: center;"><br></div><div style="text-align: center;"><a href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" target="_blank" rel="noopener" data-mce-href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" data-mce-selected="inline-boundary" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px; background-color: rgb(180, 215, 255); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2;"><img src="https://hdp.faisco.com/version2/image/hdLogo.svg" alt="FAISCO Logo" data-mce-src="https://hdp.faisco.com/version2/image/hdLogo.svg" style="max-width: 100px; margin-bottom: 15px; width: 100px; height: 27px;" class="" modifysize="100%"></a></div><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">Need some help? Hit reply to this email and our support team will reply.</p><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;"><a href="https://faisco.com/h-col-119.html?_ta=101" data-mce-href="https://faisco.com/h-col-119.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Terms &amp; Conditions</a> | <a href="https://faisco.com/h-col-113.html?_ta=101" data-mce-href="https://faisco.com/h-col-113.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Privacy Policy</a></p><p class="copyright" style="margin: 15px 0px 5px; color: rgb(170, 170, 170); font-size: 12px; font-family: Roboto, Arial, Helvetica, sans-serif; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">© 2025 - FAISCO</p></div></div></div></div>
                </div>
            </td>
        </tr>
    </table>
    </div>
    '''
    chinese_char = r'，。！？；：（）《》【】“”\‘\’'
    english_char = r',.!?;:()<>[]""\'\''
    table = str.maketrans(chinese_char, english_char)
    message1 = message1.translate(table)

    date2_chinese = '2025年8月13日'
    date2_english = 'August 13, 2025'
    message2 = f'''
    <div style="width:100%; position:relative; zoom:1; text-align:left;">
    <table cellspacing="0" cellpadding="0" style="border-spacing:0; width:100%;">
        <tr valign="top">
            <td>
                <div style="padding:10px;">
                    <div class="mailBody"><div style="font:14px/1.5 'Lucida Grande'; color:#333;">
                        <p data-start="61" data-end="76">亲爱的用户，</p>
                        <p data-start="78" data-end="133">感谢您选择 FAISCO PRO 版本作为您的营销工具！我们想提醒您，您的订阅将在 <strong>3天后</strong> 自动续订。</p>
                        <ul data-start="135" data-end="199">
                            <li data-start="135" data-end="158"><strong>订阅计划</strong>：FAISCO PRO</li>
                            <li data-start="159" data-end="175"><strong>价格</strong>：$69/月</li>
                            <li data-start="176" data-end="199"><strong>续订日期</strong>：{date2_chinese}</li>
                        </ul>
                        <p data-start="201" data-end="252">如果您希望继续享受PRO版本的强大功能，无需采取任何行动，我们将使用您之前提供的支付方式完成续订。</p>
                        <p data-start="254" data-end="292">如需更新支付信息或对订阅进行调整，请直接回复本邮件，我们将竭诚为您服务。</p>
                        <p data-start="294" data-end="328">感谢您对FAISCO的支持！期待继续为您提供高效的营销解决方案。</p>
                        <p data-start="330" data-end="346">祝好，<br data-start="333" data-end="336">FAISCO团队</p>
                        
                        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">

                        <p>Dear User,</p>
                        <p>Thank you for choosing FAISCO PRO as your marketing tool! We'd like to remind you that your subscription will automatically renew in <strong>3 days</strong>.</p>
                        <ul>
                            <li><strong>Plan</strong>: FAISCO PRO</li>
                            <li><strong>Price</strong>: $69/month</li>
                            <li><strong>Renewal Date</strong>: {date2_english}</li>
                        </ul>
                        <p>If you'd like to continue enjoying the powerful features of the PRO plan, no action is required. The renewal will be processed using the payment method you previously provided.</p>
                        <p>Should you need to update your payment information or make adjustments to your subscription, please reply to this email, and we’ll be happy to assist you.</p>
                        <p>Thank you for your support of FAISCO! We look forward to continuing to provide you with efficient marketing solutions.</p>
                        <p>Best regards,<br>The FAISCO Team</p>
                    </div></div>
                    <div style="text-align: center;"><a href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" target="_blank" rel="noopener" data-mce-href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" data-mce-selected="inline-boundary" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px; background-color: rgb(180, 215, 255); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2;"><img src="https://hdp.faisco.com/version2/image/hdLogo.svg" alt="FAISCO Logo" data-mce-src="https://hdp.faisco.com/version2/image/hdLogo.svg" style="max-width: 100px; margin-bottom: 15px; width: 100px; height: 27px;" class="" modifysize="100%"></a></div><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">Need some help? Hit reply to this email and our support team will reply.</p><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;"><a href="https://faisco.com/h-col-119.html?_ta=101" data-mce-href="https://faisco.com/h-col-119.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Terms &amp; Conditions</a> | <a href="https://faisco.com/h-col-113.html?_ta=101" data-mce-href="https://faisco.com/h-col-113.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Privacy Policy</a></p><p class="copyright" style="margin: 15px 0px 5px; color: rgb(170, 170, 170); font-size: 12px; font-family: Roboto, Arial, Helvetica, sans-serif; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">© 2025 - FAISCO</p>
                </div>
            </td>
        </tr>
    </table>
    </div>
    '''

    date3_chinese = '2025年7月16日' # 假设失败的扣款日期
    date3_english = 'July 16, 2025' # Assuming the failed payment date
    message3 = f'''
    <div style="width:100%; position:relative; zoom:1; text-align:left;">
    <table cellspacing="0" cellpadding="0" style="border-spacing:0; width:100%;">
        <tr valign="top">
            <td>
                <div style="padding:10px;">
                    <div class="mailBody"><div style="font:14px/1.5 'Lucida Grande'; color:#333;">
                        <p>亲爱的用户，</p>
                        <p>我们注意到您最近的 FAISCO BASIC 订阅付款失败。为了确保您能继续使用我们的服务，请尽快更新您的付款信息并重新订阅。</p>
                        <ul>
                            <li><strong>订阅计划</strong>：FAISCO BASIC</li>
                            <li><strong>状态</strong>：付款失败</li>
                            <li><strong>失败日期</strong>：{date3_chinese}</li>
                        </ul>
                        <p>如果您不重新订阅，您的账户可能会降级，影响您使用部分高级功能。请点击下方链接登录账号并更新您的订阅：</p>
                        <p><a href="https://i.faisco.com" target="_blank" rel="noopener">点击登录 FAISCO 平台</a></p>
                        <p>如果您有任何疑问或需要帮助，请随时回复本邮件与我们联系。</p>
                        <p>感谢您对FAISCO的支持！</p>
                        <p>祝好，<br>FAISCO团队</p>
                        
                        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">

                        <p>Dear User,</p>
                        <p>We noticed that your recent payment for the FAISCO BASIC subscription failed. To ensure continued access to our services, please update your payment information and resubscribe as soon as possible.</p>
                        <ul>
                            <li><strong>Plan</strong>: FAISCO BASIC</li>
                            <li><strong>Status</strong>: Payment Failed</li>
                            <li><strong>Failed Date</strong>: {date3_english}</li>
                        </ul>
                        <p>If you do not resubscribe, your account may be downgraded, affecting your access to some premium features. Please click the link below to log in to your account and update your subscription:</p>
                        <p><a href="https://i.faisco.com" target="_blank" rel="noopener">Login to FAISCO</a></p>
                        <p>If you have any questions or need assistance, please feel free to reply to this email.</p>
                        <p>Thank you for your support of FAISCO!</p>
                        <p>Best regards,<br>The FAISCO Team</p>
                    </div></div>
                    <div style="text-align: center;"><a href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" target="_blank" rel="noopener" data-mce-href="https://faisco.com/?_ta=101&amp;utm_source=email&amp;utm_source=email_footer" data-mce-selected="inline-boundary" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px; background-color: rgb(180, 215, 255); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2;"><img src="https://hdp.faisco.com/version2/image/hdLogo.svg" alt="FAISCO Logo" data-mce-src="https://hdp.faisco.com/version2/image/hdLogo.svg" style="max-width: 100px; margin-bottom: 15px; width: 100px; height: 27px;" class="" modifysize="100%"></a></div><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">Need some help? Hit reply to this email and our support team will reply.</p><p style="margin: 5px 0px; color: rgb(102, 102, 102); font-family: Roboto, Arial, Helvetica, sans-serif; font-size: 13px; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;"><a href="https://faisco.com/h-col-119.html?_ta=101" data-mce-href="https://faisco.com/h-col-119.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Terms &amp; Conditions</a> | <a href="https://faisco.com/h-col-113.html?_ta=101" data-mce-href="https://faisco.com/h-col-113.html?_ta=101" style="text-decoration: none; color: rgb(0, 123, 255); margin: 0px 10px;" target=_blank>Privacy Policy</a></p><p class="copyright" style="margin: 15px 0px 5px; color: rgb(170, 170, 170); font-size: 12px; font-family: Roboto, Arial, Helvetica, sans-serif; font-variant-ligatures: normal; orphans: 2; text-align: center; widows: 2; text-decoration-thickness: initial;">© 2025 - FAISCO</p>
                </div>
            </td>
        </tr>
    </table>
    </div>
    '''

    
    # 每月8号中午12点，给 <EMAIL> 发续订邮件（10号到期，提前3天发）
    scheduler.send_immediate(
        to_email=["<EMAIL>", "<EMAIL>"],  
        # to_email=["<EMAIL>", "<EMAIL>"],  
        subject="Reminder: Your FAISCO PRO Subscription is About to Renew",
        message=message1,
        html=True,
        individual_emails=True  # 分别发送单独的邮件给每个收件人
    )
    
    '''
    # 每月11中午12点，给 <EMAIL> 发续订邮件（13号到期，每月11号发）
    scheduler.send_immediate(
        to_email=["<EMAIL>","<EMAIL>"],  # ["<EMAIL>"]
        subject="您的FAISCO PRO订阅即将续订",
        message=message2,
        html=True,
        individual_emails=True  # 分别发送单独的邮件给每个收件人
    )
    '''
    '''
    # 发送扣款失败提醒邮件
    scheduler.send_immediate(
        to_email=["<EMAIL>","<EMAIL>"],  # 替换为实际的用户邮箱
        subject="重要：您的FAISCO BASIC订阅付款失败",
        message=message3,
        html=True,
        individual_emails=True
    )


    
    # 定时发送示例1：设置每月15号早上10点发送邮件
    scheduler.schedule_monthly(
        day=12,
        time_str="12:10",
        to_email=["<EMAIL>"],
        subject="每月报告",
        message=message1
    )

    # 定时发送示例2：设置每月1号下午2点发送邮件（多个收件人，HTML格式）
    scheduler.schedule_monthly(
        day=12,
        time_str="12:30",
        to_email=["<EMAIL>", "<EMAIL>"],
        subject="月初汇报",
        message=message,
        html=True
    )

    # 运行调度器前，可以先测试立即发送功能
    # 取消注释下面的代码进行测试
    # success = scheduler.send_immediate(
    #     to_email="<EMAIL>",
    #     subject="测试邮件",
    #     message="这是一封测试邮件，用于验证邮件发送功能是否正常工作。"
    # )
    # if success:
    #     print("测试邮件发送成功！")
    # else:
    #     print("测试邮件发送失败，请检查邮箱配置。")

    # 运行调度器
    scheduler.run()
    '''