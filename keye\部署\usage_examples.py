#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye-VL 使用示例
作者: Python猫娘 🐾
功能: 展示Keye-VL多模态大语言模型的各种使用方法
"""

from transformers import AutoModel, AutoTokenizer, AutoProcessor
from keye_vl_utils import process_vision_info

def load_model():
    """加载Keye-VL模型"""
    print("🚀 正在加载Keye-VL模型...")
    
    # 模型路径 - 使用官方预训练模型
    model_path = "Kwai-Keye/Keye-VL-1.5-8B"
    
    try:
        # 加载模型
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
            trust_remote_code=True,
        )
        
        # 加载处理器
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        
        print("✅ 模型加载成功")
        return model, processor
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("💡 提示: 首次使用需要下载模型文件，请确保网络连接正常")
        return None, None

def example_image_analysis():
    """图像分析示例"""
    print("\n🖼️ 图像分析示例")
    print("=" * 40)
    
    model, processor = load_model()
    if not model or not processor:
        return
    
    # 图像分析消息
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "请详细描述这张图片的内容。"},
            ],
        }
    ]
    
    try:
        # 处理输入
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            **mm_processor_kwargs
        )
        inputs = inputs.to("cuda" if hasattr(model, "cuda") else "cpu")
        
        # 生成回答
        print("🤔 正在分析图片...")
        generated_ids = model.generate(**inputs, max_new_tokens=512)
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        print("🎯 分析结果:")
        print(output_text[0])
        
    except Exception as e:
        print(f"❌ 图像分析失败: {e}")

def example_video_analysis():
    """视频分析示例"""
    print("\n🎬 视频分析示例")
    print("=" * 40)
    
    model, processor = load_model()
    if not model or not processor:
        return
    
    # 视频分析消息
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "video",
                    "video": "http://s2-11508.kwimgs.com/kos/nlav11508/MLLM/videos_caption/98312843263.mp4",
                },
                {"type": "text", "text": "请描述这个视频的内容和主要情节。"},
            ],
        }
    ]
    
    try:
        # 处理输入
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            **mm_processor_kwargs
        )
        inputs = inputs.to("cuda" if hasattr(model, "cuda") else "cpu")
        
        # 生成回答
        print("🤔 正在分析视频...")
        generated_ids = model.generate(**inputs, max_new_tokens=512)
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        print("🎯 分析结果:")
        print(output_text[0])
        
    except Exception as e:
        print(f"❌ 视频分析失败: {e}")

def example_thinking_mode():
    """思维模式示例"""
    print("\n🧠 思维模式示例")
    print("=" * 40)
    
    model, processor = load_model()
    if not model or not processor:
        return
    
    # 思维模式消息 (添加 /think 后缀)
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "分析这张图片中的情感表达和艺术风格。/think"},
            ],
        }
    ]
    
    try:
        # 处理输入
        text = processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
            **mm_processor_kwargs
        )
        inputs = inputs.to("cuda" if hasattr(model, "cuda") else "cpu")
        
        # 生成回答
        print("🤔 正在深度思考...")
        generated_ids = model.generate(**inputs, max_new_tokens=1024)
        generated_ids_trimmed = [
            out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
        ]
        output_text = processor.batch_decode(
            generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )
        
        print("🎯 深度分析结果:")
        print(output_text[0])
        
    except Exception as e:
        print(f"❌ 思维模式分析失败: {e}")

def main():
    """主函数"""
    print("🐾 Keye-VL 使用示例演示")
    print("由Python猫娘制作 ✨")
    print()
    
    print("📋 可用示例:")
    print("1. 图像分析")
    print("2. 视频分析") 
    print("3. 思维模式")
    print()
    
    choice = input("请选择要运行的示例 (1-3, 或按Enter运行所有): ").strip()
    
    if choice == "1" or choice == "":
        example_image_analysis()
    
    if choice == "2" or choice == "":
        example_video_analysis()
        
    if choice == "3" or choice == "":
        example_thinking_mode()
    
    print("\n🎉 示例演示完成！")
    print("💡 你可以修改这些示例来适应你的具体需求")

if __name__ == "__main__":
    main()
