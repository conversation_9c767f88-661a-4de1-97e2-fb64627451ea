#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Keye-VL 官方示例演示
基于 https://github.com/Kwai-Keye/Keye 官方文档
作者: Python猫娘 🐾
"""

from transformers import AutoModel, AutoTokenizer, AutoProcessor
from keye_vl_utils import process_vision_info
import torch

def main():
    print("🐾 Keye-VL 多模态大语言模型演示")
    print("=" * 50)
    
    # 模型路径 - 使用官方最新版本
    model_path = "Kwai-Keye/Keye-VL-1.5-8B"
    
    print(f"🚀 正在加载模型: {model_path}")
    print("⏳ 首次运行需要下载约8GB模型文件，请耐心等待...")
    
    try:
        # 加载模型 - 官方推荐配置
        model = AutoModel.from_pretrained(
            model_path,
            torch_dtype="auto",
            device_map="auto",
            trust_remote_code=True,
        )
        
        # 推荐启用flash_attention_2以获得更好的加速和内存节省
        # model = AutoModel.from_pretrained(
        #     model_path,
        #     torch_dtype=torch.bfloat16,
        #     attn_implementation="flash_attention_2",
        #     device_map="auto",
        #     trust_remote_code=True,
        # )
        
        # 加载处理器
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        
        # 可选：设置视觉token范围以平衡性能和成本
        # min_pixels = 256*28*28
        # max_pixels = 1280*28*28
        # processor = AutoProcessor.from_pretrained(
        #     model_path, 
        #     min_pixels=min_pixels, 
        #     max_pixels=max_pixels, 
        #     trust_remote_code=True
        # )
        
        print("✅ 模型加载成功！")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("💡 请检查网络连接和系统资源")
        return
    
    # 示例1：图像分析 - 非思维模式
    print("\n🖼️ 示例1：图像分析（非思维模式）")
    print("-" * 30)
    
    messages_no_think = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "Describe this image./no_think"},
            ],
        }
    ]
    
    try:
        result = run_inference(model, processor, messages_no_think)
        print("🎯 分析结果:")
        print(result)
    except Exception as e:
        print(f"❌ 推理失败: {e}")
    
    # 示例2：图像分析 - 自动思维模式
    print("\n🧠 示例2：图像分析（自动思维模式）")
    print("-" * 30)
    
    messages_auto_think = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "Describe this image."},
            ],
        }
    ]
    
    try:
        result = run_inference(model, processor, messages_auto_think)
        print("🎯 分析结果:")
        print(result)
    except Exception as e:
        print(f"❌ 推理失败: {e}")
    
    # 示例3：图像分析 - 强制思维模式
    print("\n🤔 示例3：图像分析（强制思维模式）")
    print("-" * 30)
    
    messages_think = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": "https://s1-11508.kwimgs.com/kos/nlav11508/mllm_all/ziran_jiafeimao_11.jpg",
                },
                {"type": "text", "text": "Describe this image./think"},
            ],
        }
    ]
    
    try:
        result = run_inference(model, processor, messages_think, max_tokens=1024)
        print("🎯 深度分析结果:")
        print(result)
    except Exception as e:
        print(f"❌ 推理失败: {e}")
    
    # 示例4：视频分析
    print("\n🎬 示例4：视频分析")
    print("-" * 30)
    
    video_messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "video",
                    "video": "http://s2-11508.kwimgs.com/kos/nlav11508/MLLM/videos_caption/98312843263.mp4",
                },
                {"type": "text", "text": "Describe this video."},
            ],
        }
    ]
    
    try:
        result = run_inference(model, processor, video_messages)
        print("🎯 视频分析结果:")
        print(result)
    except Exception as e:
        print(f"❌ 视频分析失败: {e}")
    
    print("\n🎉 所有示例演示完成！")
    print("💡 你可以修改上面的示例来测试自己的图片和视频")

def run_inference(model, processor, messages, max_tokens=512):
    """运行推理的通用函数"""
    
    # 准备推理输入
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    
    image_inputs, video_inputs, mm_processor_kwargs = process_vision_info(messages)
    
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
        **mm_processor_kwargs
    )
    
    # 将输入移动到合适的设备
    device = next(model.parameters()).device
    inputs = inputs.to(device)
    
    # 生成输出
    print("🤔 正在推理中...")
    generated_ids = model.generate(**inputs, max_new_tokens=max_tokens)
    
    # 解码输出
    generated_ids_trimmed = [
        out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    
    output_text = processor.batch_decode(
        generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
    )
    
    return output_text[0] if output_text else "无输出"

if __name__ == "__main__":
    print("🐾 欢迎使用Keye-VL多模态大语言模型！")
    print("基于官方示例: https://github.com/Kwai-Keye/Keye")
    print()
    
    # 检查基本环境
    print("🔍 环境检查:")
    print(f"Python版本: {torch.__version__ if 'torch' in globals() else '未安装torch'}")
    print(f"CUDA可用: {torch.cuda.is_available() if 'torch' in globals() else '未知'}")
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        print(f"当前CUDA设备: {torch.cuda.get_device_name()}")
    print()
    
    main()
